import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsString, IsOptional, IsEnum } from 'class-validator';
import { Role } from 'src/common/enum/role.enum';

export class QuickCreateUserDto {
  @ApiProperty({
    description: '<PERSON><PERSON> của user (bắt buộc)',
    example: '<EMAIL>',
  })
  @IsEmail({}, { message: '<PERSON><PERSON> không hợp lệ' })
  email: string;

  @ApiProperty({
    description: 'Tên đầy đủ của user (bắt buộc)',
    example: 'Nguyễn Văn A',
  })
  @IsString({ message: 'Tên đầy đủ phải là chuỗi' })
  fullName: string;

  @ApiProperty({
    description: 'Role của user (tùy chọn)',
    enum: Role,
    default: Role.USER,
    required: false,
  })
  @IsEnum(Role)
  @IsOptional()
  role?: Role = Role.USER;

  @ApiProperty({
    description: 'S<PERSON> điện thoại (tùy chọn)',
    example: '+84123456789',
    required: false,
  })
  @IsString()
  @IsOptional()
  phoneNumber?: string;

  @ApiProperty({
    description: 'Avatar URL (tùy chọn)',
    example: 'https://example.com/avatar.jpg',
    required: false,
  })
  @IsString()
  @IsOptional()
  avatarUrl?: string;

  @ApiProperty({
    description: 'Công ty (tùy chọn)',
    example: 'Bright Company',
    required: false,
  })
  @IsString()
  @IsOptional()
  company?: string;

  @ApiProperty({
    description: 'Nghề nghiệp (tùy chọn)',
    example: 'Software Developer',
    required: false,
  })
  @IsString()
  @IsOptional()
  occupation?: string;

  @ApiProperty({
    description: 'Gửi email chào mừng (tùy chọn)',
    default: true,
    required: false,
  })
  @IsOptional()
  sendWelcomeEmail?: boolean = true;
}
