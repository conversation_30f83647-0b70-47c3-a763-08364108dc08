import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { ValidationPipe } from '@nestjs/common';
import { json, urlencoded } from 'express';
import { AppService } from './app.service';
import { GlobalExceptionFilter } from './common/services/global-exception.filter';
import { RabbitMQService } from './common/services/rabbitmq.service';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // Cấu hình body parser cho file upload
  app.use(json({ limit: '10mb' }));
  app.use(urlencoded({ extended: true, limit: '10mb' }));

  // Swagger config
  const config = new DocumentBuilder()
    .setTitle('Bright User API')
    .setDescription('API documentation for Bright User service')
    .setVersion('1.0')
    .addBearerAuth(
      {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        name: 'JWT',
        description: 'Enter JWT token',
        in: 'header',
      },
      'JWT-auth', // This name here is important for references
    )
    .build();
  const document = SwaggerModule.createDocument(app, config);
  const swaggerService = app.get(AppService);
  swaggerService.setDocument(document);
  SwaggerModule.setup('swagger', app, document, {
    swaggerOptions: {
      persistAuthorization: true,
    },
  });

  app.enableCors({
    origin: (origin, callback) => {
      console.log(origin);
      const allowedOrigins = [
        'https://jup-admin.vercel.app',
        'https://swagger-ui-swart.vercel.app',
        'http://localhost:3000',
        'http://localhost:4321',
        'http://localhost:9000',
        'https://bright-admin.vercel.app',
        'http://localhost:5173',
        '*',
      ];

      if (!origin || allowedOrigins.includes(origin)) {
        callback(null, true);
      } else {
        console.log('Not allowed by CORS');
        callback(new Error('Not allowed by CORS'));
      }
    },
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',
    preflightContinue: false,
    credentials: true,
    optionsSuccessStatus: 204,
    allowedHeaders: [
      'Accept',
      'X-Client-Device',
      'x-Client-Ip',
      'x-Client-Timezone',
      'x-Client-Lang',
      'x-Client-Location',
      'x-Client-Platform',
      'Authentication',
      'Authorization',
      'authorization',
      'Access-control-allow-credentials',
      'Access-control-allow-headers',
      'Access-control-allow-methods',
      'Access-control-allow-origin',
      'User-Agent',
      'Referer',
      'Accept-Encoding',
      'Accept-Language',
      'Access-Control-Request-Headers',
      'Cache-Control',
      'Pragma',
      'content-type',
    ],
  });

  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
    }),
  );

  const rabbitMQService = app.get(RabbitMQService);
  app.useGlobalFilters(new GlobalExceptionFilter(rabbitMQService));

  await app.listen(process.env.PORT ?? 3000);
}
bootstrap();
