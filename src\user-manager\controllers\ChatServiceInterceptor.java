// Chat Service HTTP Request Interceptor for User Service API calls
// This interceptor automatically adds authentication headers to all requests

package com.bright.chat.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpRequest;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * HTTP Request Interceptor for Chat Service to User Service communication
 * Automatically adds authentication and service identification headers
 */
@Component
@Slf4j
public class ChatServiceInterceptor implements ClientHttpRequestInterceptor {

    @Value("${user-service.api-key}")
    private String serviceApiKey;

    @Value("${spring.application.name:chat-service}")
    private String serviceName;

    @Value("${spring.application.version:1.0.0}")
    private String serviceVersion;

    @Override
    public ClientHttpResponse intercept(
            HttpRequest request, 
            byte[] body, 
            ClientHttpRequestExecution execution
    ) throws IOException {

        // Add API-Key authentication
        if (serviceApiKey != null && !serviceApiKey.isEmpty()) {
            request.getHeaders().set("X-API-Key", serviceApiKey);
            log.debug("Adding API-Key authentication to: {} (key: {}****)", 
                    request.getURI(),
                    serviceApiKey.substring(0, Math.min(4, serviceApiKey.length())));
        } else {
            log.warn("No API-Key available for request to: {}", request.getURI());
        }

        // Add service identification headers
        request.getHeaders().set("X-Service-Name", serviceName);
        request.getHeaders().set("X-Service-Version", serviceVersion);

        // Add content type if not already set
        if (!request.getHeaders().containsKey("Content-Type")) {
            request.getHeaders().set("Content-Type", "application/json");
        }

        log.debug("Making request to User Service: {} {} with service: {}", 
                request.getMethod(), request.getURI(), serviceName);

        return execution.execute(request, body);
    }
}

// Configuration class to register the interceptor
@Configuration
@EnableConfigurationProperties
public class RestTemplateConfig {

    @Autowired
    private ChatServiceInterceptor chatServiceInterceptor;

    @Bean
    @Primary
    public RestTemplate restTemplate() {
        RestTemplate restTemplate = new RestTemplate();
        
        // Add the interceptor to all requests
        restTemplate.getInterceptors().add(chatServiceInterceptor);
        
        // Optional: Add error handler
        restTemplate.setErrorHandler(new UserServiceErrorHandler());
        
        return restTemplate;
    }

    /**
     * Custom error handler for User Service responses
     */
    public static class UserServiceErrorHandler implements ResponseErrorHandler {
        
        private static final Logger log = LoggerFactory.getLogger(UserServiceErrorHandler.class);

        @Override
        public boolean hasError(ClientHttpResponse response) throws IOException {
            return response.getStatusCode().series() == HttpStatus.Series.CLIENT_ERROR ||
                   response.getStatusCode().series() == HttpStatus.Series.SERVER_ERROR;
        }

        @Override
        public void handleError(ClientHttpResponse response) throws IOException {
            String responseBody = StreamUtils.copyToString(response.getBody(), StandardCharsets.UTF_8);
            
            log.error("User Service API error: {} {} - Response: {}", 
                    response.getStatusCode(), 
                    response.getStatusText(), 
                    responseBody);

            // You can throw custom exceptions here based on status codes
            if (response.getStatusCode() == HttpStatus.UNAUTHORIZED) {
                throw new UserServiceAuthenticationException("Invalid API key or authentication failed");
            } else if (response.getStatusCode() == HttpStatus.NOT_FOUND) {
                throw new UserNotFoundException("User not found in User Service");
            } else {
                throw new UserServiceException("User Service API error: " + response.getStatusCode());
            }
        }
    }
}

// Custom exceptions
public class UserServiceException extends RuntimeException {
    public UserServiceException(String message) {
        super(message);
    }
}

public class UserServiceAuthenticationException extends UserServiceException {
    public UserServiceAuthenticationException(String message) {
        super(message);
    }
}

public class UserNotFoundException extends UserServiceException {
    public UserNotFoundException(String message) {
        super(message);
    }
}

// Application properties example
/*
# application.yml or application.properties

user-service:
  base-url: http://localhost:3000
  api-key: ${USER_SERVICE_API_KEY:your-secure-api-key-here}

spring:
  application:
    name: chat-service
    version: 1.0.0

# Environment variable
# USER_SERVICE_API_KEY=your-actual-api-key-here
*/
