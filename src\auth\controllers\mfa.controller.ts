import {
  Body,
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  Post,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { MFAService } from '../services/mfa.service';
import {
  DisableMFADto,
  EnableMFADto,
  GenerateMFASecretDto,
  GetMFAStatusDto,
  MFAResponseDto,
  MFASecretResponseDto,
  MFAStatusResponseDto,
  SaveMFASecretDto,
  VerifyMFADto,
} from '../dto/mfa.dto';
import { FirebaseAuthGuard } from '../guards/auth.guards';
import { CurrentUser } from '../decorators/auth.decorators';
import { User } from '../../user-manager/entities/user.entity';

@ApiTags('Multi-Factor Authentication (MFA)')
@Controller('auth/mfa')
@UseGuards(FirebaseAuthGuard)
@ApiBearerAuth('JWT-auth')
export class MFAController {
  constructor(private readonly mfaService: MFAService) {}

  @Post('generate-secret')
  @ApiOperation({
    summary: 'Generate MFA secret',
    description: 'Tạo MFA secret key và QR code cho user',
  })
  @ApiResponse({
    status: 201,
    description: 'MFA secret generated successfully',
    type: MFASecretResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - User not found or MFA already enabled',
  })
  async generateMFASecret(
    @Body() generateMFASecretDto: GenerateMFASecretDto,
  ): Promise<MFASecretResponseDto> {
    const { userId } = generateMFASecretDto;

    const result = await this.mfaService.generateMFASecret(userId);

    return result;
  }

  @Post('enable')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Enable MFA',
    description: 'Kích hoạt MFA cho user sau khi verify token',
  })
  @ApiResponse({
    status: 200,
    description: 'MFA enabled successfully',
    type: MFAResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - User not found or MFA already enabled',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid MFA token',
  })
  async enableMFA(@Body() enableMFADto: EnableMFADto): Promise<MFAResponseDto> {
    const { userId, token } = enableMFADto;

    const result = await this.mfaService.enableMFA(userId, token);

    return result;
  }

  @Post('disable')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Disable MFA',
    description: 'Tắt MFA cho user sau khi verify token',
  })
  @ApiResponse({
    status: 200,
    description: 'MFA disabled successfully',
    type: MFAResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - User not found or MFA not enabled',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid MFA token',
  })
  async disableMFA(
    @Body() disableMFADto: DisableMFADto,
  ): Promise<MFAResponseDto> {
    const { userId, token } = disableMFADto;

    const result = await this.mfaService.disableMFA(userId, token);

    return result;
  }

  @Post('verify')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Verify MFA token',
    description: 'Verify MFA token từ authentication app',
  })
  @ApiResponse({
    status: 200,
    description: 'MFA token verified successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        message: { type: 'string' },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - User not found or MFA not enabled',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid MFA token',
  })
  async verifyMFA(
    @Body() verifyMFADto: VerifyMFADto,
  ): Promise<{ success: boolean; message: string }> {
    const { userId, token } = verifyMFADto;

    const isValid = await this.mfaService.verifyMFA(userId, token);

    if (isValid) {
      return {
        success: true,
        message: 'MFA token verified successfully',
      };
    } else {
      return {
        success: false,
        message: 'Invalid MFA token',
      };
    }
  }

  @Get('status/:userId')
  @ApiOperation({
    summary: 'Get MFA status',
    description: 'Lấy trạng thái MFA của user',
  })
  @ApiResponse({
    status: 200,
    description: 'MFA status retrieved successfully',
    type: MFAStatusResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - User not found',
  })
  async getMFAStatus(
    @Body() getMFAStatusDto: GetMFAStatusDto,
  ): Promise<MFAStatusResponseDto> {
    const { userId } = getMFAStatusDto;

    return await this.mfaService.getMFAStatus(userId);
  }

  @Post('save-secret')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Save MFA secret',
    description: 'Lưu MFA secret vào database (internal use)',
  })
  @ApiResponse({
    status: 200,
    description: 'MFA secret saved successfully',
    type: MFAResponseDto,
  })
  async saveMFASecret(
    @Body() saveMFASecretDto: SaveMFASecretDto,
  ): Promise<MFAResponseDto> {
    const { userId, secret } = saveMFASecretDto;

    await this.mfaService.saveMFASecret(userId, secret);

    return {
      success: true,
      message: 'MFA secret saved successfully',
    };
  }

  @Get('my-status')
  @ApiOperation({
    summary: 'Get current user MFA status',
    description: 'Lấy trạng thái MFA của user hiện tại',
  })
  @ApiResponse({
    status: 200,
    description: 'MFA status retrieved successfully',
    type: MFAStatusResponseDto,
  })
  async getMyMFAStatus(
    @CurrentUser() user: User,
  ): Promise<MFAStatusResponseDto> {
    return await this.mfaService.getMFAStatus(user.id);
  }

  @Post('my-generate-secret')
  @ApiOperation({
    summary: 'Generate MFA secret for current user',
    description: 'Tạo MFA secret cho user hiện tại',
  })
  @ApiResponse({
    status: 201,
    description: 'MFA secret generated successfully',
    type: MFASecretResponseDto,
  })
  async generateMyMFASecret(
    @CurrentUser() user: User,
  ): Promise<MFASecretResponseDto> {
    return await this.mfaService.generateMFASecret(user.id);
  }

  @Post('my-enable')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Enable MFA for current user',
    description: 'Kích hoạt MFA cho user hiện tại',
  })
  @ApiResponse({
    status: 200,
    description: 'MFA enabled successfully',
    type: MFAResponseDto,
  })
  async enableMyMFA(
    @CurrentUser() user: User,
    @Body() body: { token: string },
  ): Promise<MFAResponseDto> {
    return await this.mfaService.enableMFA(user.id, body.token);
  }

  @Post('my-disable')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Disable MFA for current user',
    description: 'Tắt MFA cho user hiện tại',
  })
  @ApiResponse({
    status: 200,
    description: 'MFA disabled successfully',
    type: MFAResponseDto,
  })
  async disableMyMFA(
    @CurrentUser() user: User,
    @Body() body: { token: string },
  ): Promise<MFAResponseDto> {
    const result = await this.mfaService.disableMFA(user.id, body.token);

    return result;
  }
}
