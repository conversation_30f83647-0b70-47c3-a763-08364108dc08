import { Injectable } from '@nestjs/common';
import * as generator from 'generate-password';

export interface PasswordOptions {
  length?: number;
  numbers?: boolean;
  symbols?: boolean;
  uppercase?: boolean;
  lowercase?: boolean;
  excludeSimilarCharacters?: boolean;
  strict?: boolean;
}

@Injectable()
export class PasswordService {
  /**
   * Generate password với options tùy chỉnh
   */
  generatePassword(options: PasswordOptions = {}): string {
    const defaultOptions: PasswordOptions = {
      length: 12,
      numbers: true,
      symbols: false,
      uppercase: true,
      lowercase: true,
      excludeSimilarCharacters: true,
      strict: true,
    };

    const finalOptions = { ...defaultOptions, ...options };

    return generator.generate(finalOptions);
  }

  /**
   * Generate password mặc định cho user mới
   */
  generateUserPassword(): string {
    return this.generatePassword({
      length: 10,
      numbers: true,
      symbols: false, // Không dùng symbols để dễ nhớ
      uppercase: true,
      lowercase: true,
      excludeSimilarCharacters: true,
      strict: true,
    });
  }

  /**
   * Generate password tạm thời
   */
  generateTemporaryPassword(): string {
    return this.generatePassword({
      length: 8,
      numbers: true,
      symbols: false,
      uppercase: true,
      lowercase: true,
      excludeSimilarCharacters: false,
      strict: false,
    });
  }

  /**
   * Generate password mạnh cho admin
   */
  generateStrongPassword(): string {
    return this.generatePassword({
      length: 16,
      numbers: true,
      symbols: true,
      uppercase: true,
      lowercase: true,
      excludeSimilarCharacters: true,
      strict: true,
    });
  }

  /**
   * Validate password strength
   */
  validatePasswordStrength(password: string): {
    isValid: boolean;
    score: number;
    feedback: string[];
  } {
    const feedback: string[] = [];
    let score = 0;

    // Kiểm tra độ dài
    if (password.length >= 8) {
      score += 1;
    } else {
      feedback.push('Mật khẩu phải có ít nhất 8 ký tự');
    }

    // Kiểm tra chữ hoa
    if (/[A-Z]/.test(password)) {
      score += 1;
    } else {
      feedback.push('Mật khẩu phải có ít nhất 1 chữ hoa');
    }

    // Kiểm tra chữ thường
    if (/[a-z]/.test(password)) {
      score += 1;
    } else {
      feedback.push('Mật khẩu phải có ít nhất 1 chữ thường');
    }

    // Kiểm tra số
    if (/\d/.test(password)) {
      score += 1;
    } else {
      feedback.push('Mật khẩu phải có ít nhất 1 số');
    }

    // Kiểm tra ký tự đặc biệt
    if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      score += 1;
    } else {
      feedback.push('Mật khẩu phải có ít nhất 1 ký tự đặc biệt');
    }

    const isValid = score >= 4; // Ít nhất 4/5 tiêu chí

    return {
      isValid,
      score,
      feedback,
    };
  }

  /**
   * Generate multiple passwords
   */
  generateMultiplePasswords(
    count: number = 5,
    options?: PasswordOptions,
  ): string[] {
    const passwords: string[] = [];
    for (let i = 0; i < count; i++) {
      passwords.push(this.generatePassword(options));
    }
    return passwords;
  }

  /**
   * Generate password với pattern cụ thể
   */
  generatePatternPassword(pattern: string): string {
    // Pattern: L = letter, N = number, S = symbol
    let password = '';

    for (const char of pattern) {
      switch (char.toUpperCase()) {
        case 'L':
          password += this.getRandomLetter();
          break;
        case 'N':
          password += this.getRandomNumber();
          break;
        case 'S':
          password += this.getRandomSymbol();
          break;
        default:
          password += char;
      }
    }

    return password;
  }

  private getRandomLetter(): string {
    const letters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    return letters[Math.floor(Math.random() * letters.length)];
  }

  private getRandomNumber(): string {
    return Math.floor(Math.random() * 10).toString();
  }

  private getRandomSymbol(): string {
    const symbols = '!@#$%^&*()_+-=[]{}|;:,.<>?';
    return symbols[Math.floor(Math.random() * symbols.length)];
  }
}
