-- Seed data for roles, permissions, and role_permissions
-- <PERSON><PERSON> dụng UUID v4 cho id, bạn có thể thay thế bằng hàm sinh UUID của DBMS nếu cần

-- 1. Insert roles
INSERT INTO roles (id, name, description, created_at) VALUES
  ('00000000-0000-0000-0000-000000000001', 'admin', 'Administrator', NOW()),
  ('00000000-0000-0000-0000-000000000002', 'normal', 'Normal user', NOW()),
  ('00000000-0000-0000-0000-000000000003', 'student', 'Student user', NOW()),
  ('00000000-0000-0000-0000-000000000004', 'teacher', 'Teacher user', NOW()),
  ('00000000-0000-0000-0000-000000000005', 'other', 'Other user', NOW()),
  ('00000000-0000-0000-0000-000000000006', 'moderator', 'Instructor/Moderator', NOW()),
  ('00000000-0000-0000-0000-000000000007', 'guest', 'Visitor/Guest', NOW());

-- 2. Insert permissions
INSERT INTO permissions (id, name, description, created_at) VALUES
  ('10000000-0000-0000-0000-000000000001', 'read', 'Read permission', NOW()),
  ('10000000-0000-0000-0000-000000000002', 'write', 'Write permission', NOW()),
  ('10000000-0000-0000-0000-000000000003', 'update', 'Update permission', NOW()),
  ('10000000-0000-0000-0000-000000000004', 'delete', 'Delete permission', NOW()),
  ('10000000-0000-0000-0000-000000000005', 'manage_users', 'Manage users', NOW()),
  ('10000000-0000-0000-0000-000000000006', 'view_reports', 'View reports', NOW());

-- 3. Insert role_permissions (mapping role với permission)
-- admin có tất cả quyền, guest chỉ có quyền read
INSERT INTO role_permissions (role_id, permission_id) VALUES
  -- admin
  ('00000000-0000-0000-0000-000000000001', '10000000-0000-0000-0000-000000000001'),
  ('00000000-0000-0000-0000-000000000001', '10000000-0000-0000-0000-000000000002'),
  ('00000000-0000-0000-0000-000000000001', '10000000-0000-0000-0000-000000000003'),
  ('00000000-0000-0000-0000-000000000001', '10000000-0000-0000-0000-000000000004'),
  ('00000000-0000-0000-0000-000000000001', '10000000-0000-0000-0000-000000000005'),
  ('00000000-0000-0000-0000-000000000001', '10000000-0000-0000-0000-000000000006'),
  -- normal
  ('00000000-0000-0000-0000-000000000002', '10000000-0000-0000-0000-000000000001'),
  ('00000000-0000-0000-0000-000000000002', '10000000-0000-0000-0000-000000000002'),
  ('00000000-0000-0000-0000-000000000002', '10000000-0000-0000-0000-000000000003'),
  -- student
  ('00000000-0000-0000-0000-000000000003', '10000000-0000-0000-0000-000000000001'),
  ('00000000-0000-0000-0000-000000000003', '10000000-0000-0000-0000-000000000002'),
  -- teacher
  ('00000000-0000-0000-0000-000000000004', '10000000-0000-0000-0000-000000000001'),
  ('00000000-0000-0000-0000-000000000004', '10000000-0000-0000-0000-000000000002'),
  ('00000000-0000-0000-0000-000000000004', '10000000-0000-0000-0000-000000000003'),
  ('00000000-0000-0000-0000-000000000004', '10000000-0000-0000-0000-000000000006'),
  -- other
  ('00000000-0000-0000-0000-000000000005', '10000000-0000-0000-0000-000000000001'),
  -- moderator
  ('00000000-0000-0000-0000-000000000006', '10000000-0000-0000-0000-000000000001'),
  ('00000000-0000-0000-0000-000000000006', '10000000-0000-0000-0000-000000000002'),
  ('00000000-0000-0000-0000-000000000006', '10000000-0000-0000-0000-000000000003'),
  ('00000000-0000-0000-0000-000000000006', '10000000-0000-0000-0000-000000000006'),
  -- guest
  ('00000000-0000-0000-0000-000000000007', '10000000-0000-0000-0000-000000000001'); 