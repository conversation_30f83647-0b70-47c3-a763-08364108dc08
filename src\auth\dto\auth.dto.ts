import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsEmail, MinLength } from 'class-validator';

export class VerifyTokenDto {
  @ApiProperty({
    description: 'Firebase ID token từ frontend',
    example:
      'eyJhbGciOiJSUzI1NiIsImtpZCI6IjFiYjFiYjFiYjFiYjFiYjFiYjFiYjFiYjFiYjFiYjFiYjFiYjFiYjFiYjFiYjEiLCJ0eXAiOiJKV1QifQ...',
  })
  @IsString({ message: 'Token phải là chuỗi' })
  @IsNotEmpty({ message: 'Token không được để trống' })
  token: string;
}

export class RefreshTokenDto {
  @ApiProperty({
    description: 'Refresh token',
    example: 'refresh_token_here',
  })
  @IsString({ message: 'Refresh token phải là chuỗi' })
  @IsNotEmpty({ message: 'Refresh token không được để trống' })
  refreshToken: string;
}

export class LoginDto {
  @ApiProperty({
    description: 'Email của user',
    example: '<EMAIL>',
  })
  @IsEmail({}, { message: 'Email không hợp lệ' })
  @IsNotEmpty({ message: '<PERSON>ail không được để trống' })
  email: string;

  @ApiProperty({
    description: 'Mật khẩu',
    example: 'password123',
    minLength: 6,
  })
  @IsString({ message: 'Mật khẩu phải là chuỗi' })
  @IsNotEmpty({ message: 'Mật khẩu không được để trống' })
  @MinLength(6, { message: 'Mật khẩu phải có ít nhất 6 ký tự' })
  password: string;
}

export class FirebaseLoginDto {
  @ApiProperty({
    description: 'Firebase ID token',
    example: 'firebase_id_token_here',
  })
  @IsString({ message: 'Firebase ID token phải là chuỗi' })
  @IsNotEmpty({ message: 'Firebase ID token không được để trống' })
  idToken: string;
}
