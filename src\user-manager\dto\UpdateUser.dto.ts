import { ApiProperty } from '@nestjs/swagger';
import {
  IsOptional,
  IsEmail,
  IsEnum,
  IsBoolean,
  IsString,
} from 'class-validator';
import { Role } from '../../common/enum/role.enum';

export class UpdateUserDto {
  @ApiProperty({
    description: 'User email',
    example: '<EMAIL>',
    required: false,
  })
  @IsOptional()
  @IsEmail({}, { message: 'Email không hợp lệ' })
  email?: string;
  @ApiProperty({
    description: 'User role',
    example: 'USER',
    enum: ['USER', 'ADMIN'],
    required: false,
  })
  @IsOptional()
  @IsEnum(Role)
  role?: string;
  @ApiProperty({
    description: 'User active status',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
  @ApiProperty({ description: 'MFA secret', required: false })
  @IsString()
  @IsOptional()
  mfaSecret?: string;
  @ApiProperty({ description: 'Use MFA secret', required: false })
  @IsBoolean()
  @IsOptional()
  useMfaSecret?: boolean;
}
