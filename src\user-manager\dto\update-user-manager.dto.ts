import { PartialType } from '@nestjs/swagger';
import { ApiProperty } from '@nestjs/swagger';
import {
  IsEmail,
  IsString,
  IsOptional,
  IsEnum,
  IsBoolean,
} from 'class-validator';
import { Role } from 'src/common/enum/role.enum';
import { CreateUserManagerDto } from './create-user-manager.dto';

export class UpdateUserManagerDto extends PartialType(CreateUserManagerDto) {
  @ApiProperty({ description: 'Email của user', required: false })
  @IsEmail({}, { message: 'Email không hợp lệ' })
  @IsOptional()
  email?: string;

  @ApiProperty({ description: 'Firebase ID của user', required: false })
  @IsString()
  @IsOptional()
  firebaseId?: string;

  @ApiProperty({ description: 'Role của user', enum: Role, required: false })
  @IsEnum(Role)
  @IsOptional()
  role?: Role;

  @ApiProperty({ description: '<PERSON>ên đầy đủ của user', required: false })
  @IsString()
  @IsOptional()
  fullName?: string;

  @ApiProperty({ description: 'Số điện thoại', required: false })
  @IsString()
  @IsOptional()
  phoneNumber?: string;

  @ApiProperty({ description: 'Avatar URL', required: false })
  @IsString()
  @IsOptional()
  avatarUrl?: string;

  @ApiProperty({ description: 'Trạng thái active của user', required: false })
  @IsBoolean()
  @IsOptional()
  is_active?: boolean;

  @ApiProperty({ description: 'MFA secret', required: false })
  @IsString()
  @IsOptional()
  mfaSecret?: string;

  @ApiProperty({ description: 'Use MFA secret', required: false })
  @IsBoolean()
  @IsOptional()
  useMfaSecret?: boolean;
}
