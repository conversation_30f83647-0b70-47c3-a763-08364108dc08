import { RoleEntity } from './role.entity';
import { <PERSON>T<PERSON><PERSON><PERSON>, Jo<PERSON><PERSON><PERSON>umn } from 'typeorm';
import { Entity, OneToMany, OneToOne, Column } from 'typeorm';
import { UserSettings } from './user-settings.entity';
import { UserProfile } from './user-profile.entity';
import { ApiProperty } from '@nestjs/swagger';
import { BaseEntity } from '../../common/base/base.entity';

@Entity('users')
export class User extends BaseEntity {
  @Column({ type: 'varchar', length: 255, unique: true })
  @ApiProperty()
  email: string;

  @Column({ type: 'varchar', length: 255, unique: true })
  @ApiProperty()
  firebaseId: string;

  @Column({ nullable: true })
  @ApiProperty({ required: false })
  mfaSecret?: string;

  @Column({ nullable: true, default: false })
  @ApiProperty({ required: false })
  useMfaSecret?: boolean;

  @ManyToOne(() => RoleEntity, { eager: true })
  @JoinColumn({ name: 'role_id' })
  role: RoleEntity;

  @Column({ type: 'boolean', default: true })
  @ApiProperty()
  is_active: boolean;

  @OneToMany(() => UserSettings, (userSettings) => userSettings.user)
  settings: UserSettings[];

  @OneToOne(() => UserProfile, (userProfile) => userProfile.user)
  profile: UserProfile;
}
