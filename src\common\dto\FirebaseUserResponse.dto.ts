import { ApiProperty } from '@nestjs/swagger';

export class FirebaseUserResponseDto {
  @ApiProperty({ description: 'Firebase UID', example: 'firebase_uid_here' })
  uid: string;

  @ApiProperty({ description: 'Firebase email', example: '<EMAIL>' })
  email: string;

  @ApiProperty({
    description: 'Firebase display name',
    example: '<PERSON> Do<PERSON>',
    required: false,
  })
  displayName?: string;
}
