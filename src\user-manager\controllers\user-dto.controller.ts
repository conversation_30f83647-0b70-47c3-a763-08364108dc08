import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  HttpCode,
  HttpStatus,
  ValidationPipe,
  UsePipes,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { UserDtoService } from '../services/user-dto.service';
import { UserManagerService } from '../services/user-manager.service';
import {
  CreateUserDto,
  CreateUserWithSettingsDto,
  UpdateUserDto,
} from '../dto/user.dto';
import { QuickCreateUserDto } from '../dto/quick-create.dto';
import { UserDto } from '../dto/user.dto';
import { UserSettingsDto } from '../dto/user-settings.dto';
import { FirebaseAuthGuard, RolesGuard } from '../../auth/guards/auth.guards';
import { Roles } from '../../auth/decorators/auth.decorators';
import { Role } from '../../common/enum/role.enum';
import { MapperService } from '../../common/services/mapper.service';
import { UserProfileDto } from '../dto/UserProfile.dto';

@ApiTags('User DTO Management')
@Controller('user-dto')
@UsePipes(new ValidationPipe({ transform: true }))
@UseGuards(FirebaseAuthGuard)
@ApiBearerAuth('JWT-auth')
export class UserDtoController {
  constructor(
    private readonly userDtoService: UserDtoService,
    private readonly userManagerService: UserManagerService,
    private readonly mapperService: MapperService,
  ) {}

  @Get()
  @ApiOperation({ summary: 'Get all users as DTOs' })
  @ApiResponse({
    status: 200,
    description: 'List of all users as DTOs',
    type: [UserDto],
  })
  @UseGuards(RolesGuard)
  @Roles(Role.ADMIN)
  async findAll(): Promise<UserDto[]> {
    return this.userDtoService.getAllUsersAsDtos();
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get user by ID as DTO' })
  @ApiParam({ name: 'id', description: 'User ID (UUID)' })
  @ApiResponse({ status: 200, description: 'User found as DTO', type: UserDto })
  @ApiResponse({ status: 404, description: 'User not found' })
  async findOne(@Param('id') id: string): Promise<UserDto> {
    return this.userDtoService.getUserByIdAsDto(id);
  }

  @Get(':id/complete')
  @ApiOperation({
    summary: 'Get user with all related data as DTO (profile, settings)',
  })
  @ApiParam({ name: 'id', description: 'User ID (UUID)' })
  @ApiResponse({ status: 200, description: 'User with all data found as DTO' })
  @ApiResponse({ status: 404, description: 'User not found' })
  async getUserWithAllData(@Param('id') id: string) {
    return this.userDtoService.getUserWithProfileAndSettingsAsDto(id);
  }

  @Get(':id/profile')
  @ApiOperation({ summary: 'Get user profile as DTO' })
  @ApiParam({ name: 'id', description: 'User ID (UUID)' })
  @ApiResponse({
    status: 200,
    description: 'User profile found as DTO',
    type: UserProfileDto,
  })
  @ApiResponse({ status: 404, description: 'User not found' })
  async getUserProfile(@Param('id') id: string) {
    return this.userDtoService.getUserWithProfileAsDto(id);
  }

  @Get(':id/settings')
  @ApiOperation({ summary: 'Get user settings as DTOs' })
  @ApiParam({ name: 'id', description: 'User ID (UUID)' })
  @ApiResponse({
    status: 200,
    description: 'User settings retrieved as DTOs',
    type: [UserSettingsDto],
  })
  @ApiResponse({ status: 404, description: 'User not found' })
  async getUserSettings(@Param('id') id: string): Promise<UserSettingsDto[]> {
    return this.userDtoService.getUserSettingsAsDtos(id);
  }

  @Get(':id/settings/object')
  @ApiOperation({ summary: 'Get user settings as object' })
  @ApiParam({ name: 'id', description: 'User ID (UUID)' })
  @ApiResponse({
    status: 200,
    description: 'User settings retrieved as object',
  })
  @ApiResponse({ status: 404, description: 'User not found' })
  async getUserSettingsAsObject(@Param('id') id: string) {
    return this.userDtoService.getUserSettingsAsObject(id);
  }

  @Post()
  @ApiOperation({ summary: 'Create a new user' })
  @ApiResponse({ status: 201, description: 'User created successfully' })
  @ApiResponse({ status: 409, description: 'User already exists' })
  @UseGuards(RolesGuard)
  @Roles(Role.ADMIN)
  async create(@Body() createUserDto: CreateUserDto) {
    const result = await this.userManagerService.create(createUserDto);
    return this.userDtoService.getUserByIdAsDto(result.user.id);
  }

  @Post('with-firebase')
  @ApiOperation({
    summary: 'Create a new user with Firebase integration and default settings',
  })
  @ApiResponse({
    status: 201,
    description: 'User created successfully with Firebase and settings',
  })
  @ApiResponse({ status: 409, description: 'User already exists' })
  @ApiResponse({ status: 400, description: 'Invalid Firebase ID or data' })
  @UseGuards(RolesGuard)
  @Roles(Role.ADMIN)
  async createWithFirebase(
    @Body() createUserWithSettingsDto: CreateUserWithSettingsDto,
  ) {
    const result = await this.userManagerService.createUserWithFirebase(
      createUserWithSettingsDto,
    );
    return this.userDtoService.getUserByIdAsDto(result.user.id);
  }

  @Post('quick-create')
  @ApiOperation({
    summary:
      'Quick create user with email and full name, auto-generate password and Firebase account',
  })
  @ApiResponse({
    status: 201,
    description: 'User created successfully with auto-generated password',
  })
  @ApiResponse({ status: 409, description: 'User already exists' })
  @ApiResponse({ status: 400, description: 'Invalid data' })
  @UseGuards(RolesGuard)
  @Roles(Role.ADMIN)
  async quickCreateUser(@Body() quickCreateUserDto: QuickCreateUserDto) {
    const result =
      await this.userManagerService.quickCreateUser(quickCreateUserDto);
    if (result.data?.user?.id) {
      return this.userDtoService.getUserByIdAsDto(result.data.user.id);
    }
    return result;
  }

  @Post('bulk-create')
  @ApiOperation({ summary: 'Bulk create multiple users' })
  @ApiResponse({ status: 201, description: 'Users created successfully' })
  @ApiResponse({ status: 400, description: 'Invalid data' })
  @UseGuards(RolesGuard)
  @Roles(Role.ADMIN)
  async bulkCreateUsers(@Body() usersData: QuickCreateUserDto[]) {
    const result = await this.userManagerService.bulkCreateUsers(usersData);
    return result;
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update user' })
  @ApiParam({ name: 'id', description: 'User ID (UUID)' })
  @ApiResponse({
    status: 200,
    description: 'User updated successfully',
    type: UserDto,
  })
  @ApiResponse({ status: 404, description: 'User not found' })
  @UseGuards(RolesGuard)
  @Roles(Role.ADMIN)
  async update(
    @Param('id') id: string,
    @Body() updateUserDto: UpdateUserDto,
  ): Promise<UserDto> {
    await this.userManagerService.update(id, updateUserDto);
    return this.userDtoService.getUserByIdAsDto(id);
  }

  @Patch(':id/settings')
  @ApiOperation({ summary: 'Update user settings' })
  @ApiParam({ name: 'id', description: 'User ID (UUID)' })
  @ApiResponse({
    status: 200,
    description: 'User settings updated successfully',
  })
  @ApiResponse({ status: 404, description: 'User not found' })
  async updateUserSettings(
    @Param('id') id: string,
    @Body() settings: Record<string, string>,
  ) {
    await this.userManagerService.updateUserSettings(id, settings);
    return this.userDtoService.getUserSettingsAsDtos(id);
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Delete user' })
  @ApiParam({ name: 'id', description: 'User ID (UUID)' })
  @ApiResponse({ status: 204, description: 'User deleted successfully' })
  @ApiResponse({ status: 404, description: 'User not found' })
  @UseGuards(RolesGuard)
  @Roles(Role.ADMIN)
  async remove(@Param('id') id: string) {
    return this.userManagerService.remove(id);
  }
}
