import {
  Controller,
  Get,
  Post,
  Body,
  UseGuards,
  Request,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { AuthService } from '../services/auth.service';
import { MapperService } from '../../common/services/mapper.service';
import { VerifyTokenDto, RefreshTokenDto } from '../dto/auth.dto';
import { FirebaseAuthGuard, RolesGuard } from '../guards/auth.guards';
import { Roles, CurrentUser } from '../decorators/auth.decorators';
import { Role } from '../../common/enum/role.enum';
import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString } from 'class-validator';
import { Request as ExpressRequest, Response } from 'express';
import { User } from '../../user-manager/entities/user.entity';

class VerifyTokenWithMFADto extends VerifyTokenDto {
  @ApiProperty({
    description: 'MFA token (required if MFA is enabled)',
    example: '123456',
    required: false,
  })
  @IsOptional()
  @IsString()
  mfaToken?: string;
}

@ApiTags('Authentication')
@Controller('auth')
export class AuthController {
  constructor(
    private readonly authService: AuthService,
    private readonly mapperService: MapperService,
  ) {}

  @Post('verify')
  @ApiOperation({ summary: 'Verify Firebase token and get user profile' })
  @ApiResponse({ status: 200, description: 'Token verified successfully' })
  @ApiResponse({ status: 401, description: 'Invalid token' })
  @HttpCode(HttpStatus.OK)
  async verifyToken(@Body() verifyTokenDto: VerifyTokenDto) {
    const result = await this.authService.verifyToken(verifyTokenDto);
    return this.mapperService.createSuccessResponse(result);
  }

  @Post('verify-with-mfa')
  @ApiOperation({
    summary: 'Verify Firebase token with MFA',
    description: 'Verify Firebase ID token với MFA token (nếu MFA được enable)',
  })
  @ApiResponse({
    status: 200,
    description: 'Token verified successfully with MFA',
  })
  @ApiResponse({
    status: 401,
    description: 'Invalid token or MFA token required',
  })
  @HttpCode(HttpStatus.OK)
  async verifyTokenWithMFA(
    @Body() verifyTokenWithMFADto: VerifyTokenWithMFADto,
  ) {
    const result = await this.authService.verifyTokenWithMFA(
      verifyTokenWithMFADto,
      verifyTokenWithMFADto.mfaToken,
    );
    return this.mapperService.createSuccessResponse(result);
  }

  @Post('refresh')
  @ApiOperation({ summary: 'Refresh JWT token' })
  @ApiResponse({ status: 200, description: 'Token refreshed successfully' })
  @ApiResponse({ status: 401, description: 'Invalid refresh token' })
  @HttpCode(HttpStatus.OK)
  async refreshToken(@Body() refreshTokenDto: RefreshTokenDto) {
    const result = await this.authService.refreshToken(
      refreshTokenDto.refreshToken,
    );
    return this.mapperService.createSuccessResponse(result);
  }

  @Post('logout')
  @UseGuards(FirebaseAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Logout user' })
  @ApiResponse({ status: 200, description: 'Logged out successfully' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @HttpCode(HttpStatus.OK)
  async logout(@CurrentUser('id') userId: string) {
    await this.authService.logout(userId);
    return {
      success: true,
      message: 'Logged out successfully',
    };
  }

  @Get('profile')
  @UseGuards(FirebaseAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Get current user profile' })
  @ApiResponse({ status: 200, description: 'Profile retrieved successfully' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async getCurrentUser(@CurrentUser('id') userId: string) {
    const user = await this.authService.getCurrentUser(userId);
    return this.mapperService.createSuccessResponse(user);
  }

  @Get('permissions')
  @UseGuards(FirebaseAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Get user permissions based on role' })
  @ApiResponse({
    status: 200,
    description: 'Permissions retrieved successfully',
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async getUserPermissions(@CurrentUser('id') userId: string) {
    const permissions = await this.authService.getUserPermissions(userId);
    return this.mapperService.createSuccessResponse(permissions);
  }

  @Get('admin-only')
  @UseGuards(FirebaseAuthGuard, RolesGuard)
  @Roles(Role.ADMIN)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Admin only endpoint - requires ADMIN role' })
  @ApiResponse({ status: 200, description: 'Admin access granted' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - requires ADMIN role' })
  adminOnly(@CurrentUser() user: User) {
    return {
      success: true,
      message: 'Admin access granted',
      user: {
        id: user.id,
        email: user.email,
        role: user.role,
      },
    };
  }

  @Get('moderator-or-admin')
  @UseGuards(FirebaseAuthGuard, RolesGuard)
  @Roles(Role.ADMIN, Role.MODERATOR)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Moderator or Admin endpoint' })
  @ApiResponse({ status: 200, description: 'Access granted' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - requires MODERATOR or ADMIN role',
  })
  moderatorOrAdmin(@CurrentUser() user: User) {
    return {
      success: true,
      message: 'Moderator or Admin access granted',
      user: {
        id: user.id,
        email: user.email,
        role: user.role,
      },
    };
  }

  @Get('public')
  @ApiOperation({ summary: 'Public endpoint - no authentication required' })
  @ApiResponse({ status: 200, description: 'Public access granted' })
  publicEndpoint() {
    return {
      success: true,
      message: 'Public endpoint - no authentication required',
      timestamp: new Date().toISOString(),
    };
  }

  @Get('protected')
  @UseGuards(FirebaseAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Protected endpoint - requires authentication' })
  @ApiResponse({ status: 200, description: 'Protected access granted' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  protectedEndpoint(@CurrentUser() user: User) {
    return {
      success: true,
      message: 'Protected endpoint - authentication required',
      user: {
        id: user.id,
        email: user.email,
        role: user.role,
      },
      timestamp: new Date().toISOString(),
    };
  }

  @Get('test-auth')
  @UseGuards(FirebaseAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Test authentication' })
  @ApiResponse({ status: 200, description: 'Authentication successful' })
  @ApiResponse({ status: 401, description: 'Authentication failed' })
  testAuth(@Request() req: ExpressRequest) {
    const user = req.user as User;
    return this.mapperService.createAuthTestResponse(user);
  }

  @Post('check-token')
  @ApiOperation({ summary: 'Check if Firebase token is valid' })
  @ApiResponse({ status: 200, description: 'Token status' })
  @ApiResponse({ status: 401, description: 'Token invalid or expired' })
  async checkToken(@Body() body: { token: string }) {
    try {
      const decodedToken = await this.authService.checkToken(body.token);
      return {
        success: true,
        message: 'Token is valid',
        data: {
          uid: decodedToken.uid,
          email: decodedToken.email,
          emailVerified: decodedToken.email_verified,
          exp: decodedToken.exp,
          iat: decodedToken.iat,
          expiresAt: new Date(decodedToken.exp * 1000).toISOString(),
          issuedAt: new Date(decodedToken.iat * 1000).toISOString(),
          currentTime: new Date().toISOString(),
          isExpired: Date.now() > decodedToken.exp * 1000,
        },
      };
    } catch (error) {
      return {
        success: false,
        message: 'Token is invalid or expired',
        error: error.message,
        data: {
          currentTime: new Date().toISOString(),
        },
      };
    }
  }

  @Get('firebase-config')
  @ApiOperation({ summary: 'Check Firebase configuration' })
  @ApiResponse({ status: 200, description: 'Firebase config status' })
  checkFirebaseConfig() {
    try {
      const projectId = process.env.FIREBASE_PROJECT_ID;
      const privateKey = process.env.FIREBASE_PRIVATE_KEY;
      const clientEmail = process.env.FIREBASE_CLIENT_EMAIL;
      const webApiKey = process.env.FIREBASE_WEB_API_KEY;

      return {
        success: true,
        message: 'Firebase configuration check',
        data: {
          projectId: projectId ? '✅ Set' : '❌ Missing',
          privateKey: privateKey ? '✅ Set' : '❌ Missing',
          clientEmail: clientEmail ? '✅ Set' : '❌ Missing',
          webApiKey: webApiKey ? '✅ Set' : '❌ Missing',
          environment: process.env.NODE_ENV || 'development',
          timestamp: new Date().toISOString(),
        },
      };
    } catch (error) {
      return {
        success: false,
        message: 'Firebase configuration error',
        error: error.message,
      };
    }
  }
}
