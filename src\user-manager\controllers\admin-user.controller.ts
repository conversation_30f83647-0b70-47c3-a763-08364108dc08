import {
  Controller,
  Get,
  Query,
  Param,
  Patch,
  Body,
  Delete,
  UseGuards,
  Res,
} from '@nestjs/common';
import { Roles } from '../../auth/decorators/auth.decorators';
import { Role } from '../../common/enum/role.enum';
import { RolesGuard } from '../../auth/guards/auth.guards';
import { UserManagerService } from '../services/user-manager.service';
import { AdminUserFilterDto, AdminUserUpdateDto } from '../dto/admin-user.dto';
import { Response } from 'express';

@UseGuards(RolesGuard)
@Roles(Role.ADMIN)
@Controller('admin/users')
export class AdminUserController {
  constructor(private readonly userManagerService: UserManagerService) {}

  @Get()
  async listUsers(@Query() query: AdminUserFilterDto, @Res() res: Response) {
    if (query.export) {
      return this.userManagerService.adminExportUsers(query, res);
    }
    const result = await this.userManagerService.adminListUsers(query);
    return res.json(result);
  }

  @Get('stats')
  async userStats() {
    return this.userManagerService.adminUserStats();
  }

  @Get(':id')
  async getUser(@Param('id') id: string) {
    return this.userManagerService.findOne(id);
  }

  @Patch(':id')
  async updateUser(@Param('id') id: string, @Body() dto: AdminUserUpdateDto) {
    return this.userManagerService.adminUpdateUser(id, dto);
  }

  @Patch(':id/lock')
  async lockUser(@Param('id') id: string) {
    return this.userManagerService.adminLockUser(id);
  }

  @Patch(':id/unlock')
  async unlockUser(@Param('id') id: string) {
    return this.userManagerService.adminUnlockUser(id);
  }

  @Delete(':id')
  async deleteUser(@Param('id') id: string) {
    return this.userManagerService.adminDeleteUser(id);
  }

  @Delete('firebase-all')
  async deleteAllFirebaseUsers() {
    return this.userManagerService.adminDeleteAllFirebaseUsers();
  }

  @Get(':id/audit-log')
  async userAuditLog(
    @Param('id') id: string,
    @Query('limit') limit = 20,
    @Query('page') page = 1,
  ) {
    return this.userManagerService.adminUserAuditLog(id, {
      limit: +limit,
      page: +page,
    });
  }

  @Get('/audit-log/export')
  async exportAuditLog(@Query() query: any, @Res() res: Response) {
    return this.userManagerService.adminExportAuditLog(query, res);
  }
}
