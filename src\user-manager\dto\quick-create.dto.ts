import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsEmail,
  IsOptional,
  IsEnum,
  IsBoolean,
} from 'class-validator';
import { Role } from '../../common/enum/role.enum';

export class QuickCreateUserDto {
  @ApiProperty({
    description: 'Email của user (bắt buộc)',
    example: '<EMAIL>',
  })
  @IsEmail({}, { message: 'Email không hợp lệ' })
  email: string;

  @ApiProperty({
    description: 'Tên đầy đủ của user (bắt buộc)',
    example: 'Nguyễn Văn A',
  })
  @IsString({ message: 'Tên đầy đủ phải là chuỗi' })
  fullName: string;

  @ApiProperty({
    description: 'Role của user',
    enum: Role,
    default: Role.USER,
    required: false,
  })
  @IsEnum(Role)
  role?: Role = Role.USER;

  @ApiProperty({
    description: '<PERSON><PERSON> điện thoại (tùy chọn)',
    example: '+84123456789',
    required: false,
  })
  @IsString()
  @IsOptional()
  phone?: string;

  @ApiProperty({
    description: 'Avatar URL (tùy chọn)',
    example: 'https://example.com/avatar.jpg',
    required: false,
  })
  @IsString()
  @IsOptional()
  avatar?: string;

  @ApiProperty({
    description: 'Công ty (tùy chọn)',
    example: 'Bright Company',
    required: false,
  })
  @IsString()
  @IsOptional()
  company?: string;

  @ApiProperty({
    description: 'Nghề nghiệp (tùy chọn)',
    example: 'Software Developer',
    required: false,
  })
  @IsString()
  @IsOptional()
  jobTitle?: string;

  @ApiProperty({
    description: 'Gửi email chào mừng (tùy chọn)',
    default: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  sendWelcomeEmail?: boolean = true;
}
