/* eslint-disable @typescript-eslint/no-redundant-type-constituents */
import { Injectable, OnModuleDestroy } from '@nestjs/common';
import { Connection, Channel, connect } from 'amqplib';

@Injectable()
export class RabbitMQService implements OnModuleDestroy {
  private connection: Connection | null = null;
  private channel: Channel | null = null;

  private async getChannel(): Promise<Channel> {
    if (this.channel) return this.channel;
    const url = process.env.RABBITMQ_URL || 'amqp://localhost';
    if (!this.connection) {
      this.connection = await connect(url);
    }
    this.channel = await this.connection.createChannel();
    return this.channel;
  }

  async publish(
    exchange: string,
    routingKey: string,
    message: any,
  ): Promise<void> {
    const channel = await this.getChannel();
    await channel.assertExchange(exchange, 'topic', { durable: true });
    channel.publish(
      exchange,
      routingKey,
      Buffer.from(JSON.stringify(message)),
      { persistent: true },
    );
  }

  /**
   * <PERSON><PERSON><PERSON> bảo queue được binding vào exchange với routing key
   */
  async bindQueueToExchange(
    queue: string,
    exchange: string,
    routingKey: string,
  ) {
    const channel = await this.getChannel();
    await channel.assertExchange(exchange, 'topic', { durable: true });
    await channel.assertQueue(queue, { durable: true });
    await channel.bindQueue(queue, exchange, routingKey);
  }

  async onModuleDestroy() {
    if (this.channel) await this.channel.close();
    if (this.connection) await this.connection.close();
  }
}
