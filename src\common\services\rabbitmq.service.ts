import { Injectable, OnModuleDestroy } from '@nestjs/common';
import * as amqp from 'amqplib';

@Injectable()
export class RabbitMQService implements OnModuleDestroy {
  private connection: amqp.Connection | null = null;
  private channel: amqp.Channel | null = null;

  private async getChannel(): Promise<amqp.Channel> {
    if (this.channel) return this.channel;
    const url = process.env.RABBITMQ_URL || 'amqp://localhost';
    if (!this.connection) {
      this.connection = await amqp.connect(url);
    }
    this.channel = await this.connection.createChannel();
    return this.channel;
  }

  async publish(
    exchange: string,
    routingKey: string,
    message: any,
  ): Promise<void> {
    const channel = await this.getChannel();
    await channel.assertExchange(exchange, 'topic', { durable: true });
    channel.publish(
      exchange,
      routingKey,
      Buffer.from(JSON.stringify(message)),
      { persistent: true },
    );
  }

  /**
   * Đảm bảo queue được binding vào exchange với routing key
   */
  async bindQueueToExchange(
    queue: string,
    exchange: string,
    routingKey: string,
  ) {
    const channel = await this.getChannel();
    await channel.assertExchange(exchange, 'topic', { durable: true });
    await channel.assertQueue(queue, { durable: true });
    await channel.bindQueue(queue, exchange, routingKey);
  }

  async onModuleDestroy() {
    if (this.channel) await this.channel.close();
    if (this.connection) await this.connection.close();
  }
}
