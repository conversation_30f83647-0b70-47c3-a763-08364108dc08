import { ApiProperty } from '@nestjs/swagger';
import { UserProfileDto } from './UserProfile.dto';

export class ProfileResponseDto {
  @ApiProperty({ description: 'Success status', example: true })
  success: boolean;

  @ApiProperty({
    description: 'Response message',
    example: 'Profile retrieved successfully',
  })
  message: string;

  @ApiProperty({ description: 'Profile data', type: UserProfileDto })
  data: {
    user?: {
      id: string;
      email: string;
      role: string;
      is_active: boolean;
    };
    profile?: UserProfileDto;
    avatarUrl?: string;
    filename?: string;
    size?: number;
    mimetype?: string;
  };
}
