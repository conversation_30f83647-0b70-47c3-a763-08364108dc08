import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  <PERSON>inColumn,
} from 'typeorm';
import { User } from './user.entity';
import { BaseEntity } from 'src/common/base/base.entity';

@Entity('user_settings')
export class UserSettings extends BaseEntity {
  @ManyToOne(() => User, (user) => user.settings, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: User;

  @Column({ type: 'varchar', length: 255 })
  setting_key: string;

  @Column({ type: 'text', nullable: true })
  setting_value: string;

  @Column({ nullable: true })
  description?: string;

  @Column({ nullable: true })
  datatype?: string;
}
