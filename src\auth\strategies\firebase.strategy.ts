import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { Strategy, ExtractJwt } from 'passport-jwt';
import { FirebaseService } from '../../user-manager/services/firebase.service';
import { UserManagerService } from '../../user-manager/services/user-manager.service';
import { Request as ExpressRequest } from 'express';

@Injectable()
export class FirebaseJwtStrategy extends PassportStrategy(
  Strategy,
  'firebase-jwt',
) {
  constructor(
    private firebaseService: FirebaseService,
    private userManagerService: UserManagerService,
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: 'firebase-jwt', // Dummy secret, we'll verify with Firebase
      passReqToCallback: true,
    });
  }

  async validate(request: ExpressRequest) {
    try {
      // Extract Firebase token from Authorization header
      const token = request.headers.authorization?.replace('Bearer ', '');
      if (!token) {
        throw new UnauthorizedException('No token provided');
      }

      // Verify Firebase token
      const decodedToken = await this.firebaseService.verifyIdToken(token);

      // Get user from database
      const user = await this.userManagerService.findByFirebaseId(
        decodedToken.uid,
      );

      if (!user || !user.is_active) {
        throw new UnauthorizedException('User not found or inactive');
      }

      // Return user object with additional info
      return {
        id: user.id,
        email: user.email,
        firebaseId: user.firebaseId,
        role: user.role,
        is_active: user.is_active,
        firebaseUid: decodedToken.uid,
        firebaseClaims: decodedToken,
      };
    } catch (error) {
      console.error('Firebase strategy error:', error);
      throw new UnauthorizedException('Invalid token');
    }
  }
}
