import { Injectable, Logger, UnauthorizedException } from '@nestjs/common';
import { FirebaseService } from '../../user-manager/services/firebase.service';
import { AdminSeeder } from '../seeders/admin.seeder';
import { Role } from '../enum/role.enum';

export interface AdminTokenResponse {
  success: boolean;
  message: string;
  data?: {
    token: string;
    user: {
      id: string;
      email: string;
      role: string;
      firebaseId: string;
      is_active: boolean;
    };
    expiresIn: number;
  };
}

@Injectable()
export class AdminTokenService {
  private readonly logger = new Logger(AdminTokenService.name);

  constructor(
    private firebaseService: FirebaseService,
    private adminSeeder: AdminSeeder,
  ) {}

  /**
   * Get admin user info and instructions for getting token
   */
  async getAdminInfo(email: string): Promise<AdminTokenResponse> {
    try {
      this.logger.log(`Getting admin info for: ${email}`);

      // Check if admin exists in database
      const admin = await this.adminSeeder.getAdminByEmail(email);
      if (!admin) {
        throw new UnauthorizedException(
          'Admin user not found. Please seed admin first.',
        );
      }

      if (!admin.is_active) {
        throw new UnauthorizedException('Admin account is inactive.');
      }

      this.logger.log(`Admin info retrieved for: ${email}`);

      return {
        success: true,
        message: 'Admin user found. Use Firebase SDK to get ID token.',
        data: {
          token: '', // Empty token - client needs to get it from Firebase SDK
          user: {
            id: admin.id,
            email: admin.email,
            role: admin.role?.name,
            firebaseId: admin.firebaseId,
            is_active: admin.is_active,
          },
          expiresIn: 0,
        },
      };
    } catch (error) {
      this.logger.error(`Error getting admin info for ${email}:`, error);
      throw error;
    }
  }

  /**
   * Get default admin info
   */
  async getDefaultAdminInfo(): Promise<AdminTokenResponse> {
    const defaultEmail = '<EMAIL>';
    return this.getAdminInfo(defaultEmail);
  }

  /**
   * Verify admin token
   */
  async verifyAdminToken(token: string): Promise<AdminTokenResponse> {
    try {
      this.logger.log('Verifying admin token');

      // Verify Firebase token
      const decodedToken = await this.firebaseService.verifyIdToken(token);

      // Check if user is admin
      const admin = await this.adminSeeder.getAdminByEmail(
        decodedToken.email || '',
      );
      if (!admin || admin.role?.name !== Role.ADMIN) {
        throw new UnauthorizedException('Not admin');
      }

      if (!admin.is_active) {
        throw new UnauthorizedException('Admin account is inactive.');
      }

      this.logger.log(
        `Admin token verified successfully for: ${decodedToken.email}`,
      );

      return {
        success: true,
        message: 'Admin token verified successfully',
        data: {
          token: token,
          user: {
            id: admin.id,
            email: admin.email,
            role: admin.role?.name,
            firebaseId: admin.firebaseId,
            is_active: admin.is_active,
          },
          expiresIn: decodedToken.exp - Math.floor(Date.now() / 1000),
        },
      };
    } catch (error) {
      this.logger.error('Error verifying admin token:', error);
      throw error;
    }
  }

  /**
   * Get instructions for getting Firebase ID token
   */
  getTokenInstructions(): {
    success: boolean;
    message: string;
    instructions: string[];
  } {
    return {
      success: true,
      message: 'Instructions for getting Firebase ID token',
      instructions: [
        '1. Install Firebase SDK in your client app',
        '2. Initialize Firebase with your project config',
        '3. Use Firebase Auth to sign in with email/password',
        '4. Get the ID token using: firebase.auth().currentUser?.getIdToken()',
        '5. Use the token in Authorization header: Bearer <token>',
        '',
        'Example (JavaScript):',
        'const user = await firebase.auth().signInWithEmailAndPassword(email, password);',
        'const token = await user.user.getIdToken();',
        '',
        'Example (cURL):',
        'curl -H "Authorization: Bearer <firebase_id_token>" http://localhost:3000/auth/profile',
      ],
    };
  }
}
