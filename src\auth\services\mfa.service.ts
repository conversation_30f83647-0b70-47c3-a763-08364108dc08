import {
  Injectable,
  Logger,
  BadRequestException,
  UnauthorizedException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as speakeasy from 'speakeasy';
import * as QRCode from 'qrcode';
import { UserManagerService } from '../../user-manager/services/user-manager.service';

export interface MFASecretResponse {
  secret: string;
  qrCode: string;
  backupCodes: string[];
}

export interface VerifyMFADto {
  userId: number;
  token: string;
}

export interface EnableMFADto {
  userId: number;
  token: string;
}

export interface DisableMFADto {
  userId: number;
  token: string;
}

@Injectable()
export class MFAService {
  private readonly logger = new Logger(MFAService.name);

  constructor(
    private configService: ConfigService,
    private userManagerService: UserManagerService,
  ) {}

  /**
   * Tạo MFA secret cho user và save vào database
   */
  async generateMFASecret(userId: string): Promise<MFASecretResponse> {
    try {
      // Ki<PERSON>m tra user tồn tại
      const user = await this.userManagerService.findOne(userId);
      if (!user) {
        throw new BadRequestException('User not found');
      }

      // Kiểm tra user đã có MFA chưa
      if (user.useMfaSecret) {
        throw new BadRequestException('MFA is already enabled for this user');
      }

      // Tạo secret key
      const secret = speakeasy.generateSecret({
        name: `${user.email} (${this.configService.get('APP_NAME', 'Bright App')})`,
        issuer: this.configService.get('APP_NAME', 'Bright App'),
        length: 32,
      });

      // Tạo QR code
      const qrCode = await QRCode.toDataURL(secret.otpauth_url);

      // Tạo backup codes
      const backupCodes = this.generateBackupCodes();

      // Save secret vào database và set cờ useMfaSecret = false (chưa enable)
      await this.userManagerService.update(userId, {
        mfaSecret: secret.base32,
        useMfaSecret: false, // Chưa enable, chỉ save secret
      });

      this.logger.log(`Generated and saved MFA secret for user ${userId}`);

      return {
        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment,@typescript-eslint/no-unsafe-member-access
        secret: secret.base32,
        qrCode,
        backupCodes,
      };
    } catch (error) {
      this.logger.error('Error generating MFA secret:', error);
      throw error;
    }
  }

  /**
   * Enable MFA cho user
   */
  async enableMFA(
    userId: string,
    token: string,
  ): Promise<{ success: boolean; message: string }> {
    try {
      // Kiểm tra user tồn tại
      const user = await this.userManagerService.findOne(userId);
      if (!user) {
        throw new BadRequestException('User not found');
      }

      // Kiểm tra user đã có MFA chưa
      if (user.useMfaSecret) {
        throw new BadRequestException('MFA is already enabled for this user');
      }

      // Kiểm tra user có secret chưa
      if (!user.mfaSecret) {
        throw new BadRequestException(
          'MFA secret not found. Please generate MFA secret first.',
        );
      }

      // Verify token
      const isValid = this.verifyToken(user.mfaSecret, token);
      if (!isValid) {
        return {
          success: false,
          message: 'Invalid MFA token',
        };
      }

      // Enable MFA - chỉ set cờ useMfaSecret = true
      await this.userManagerService.update(userId, {
        useMfaSecret: true,
      });

      this.logger.log(`Enabled MFA for user ${userId}`);

      return {
        success: true,
        message: 'MFA enabled successfully',
      };
    } catch (error) {
      this.logger.error('Error enabling MFA:', error);
      throw error;
    }
  }

  /**
   * Disable MFA cho user
   */
  async disableMFA(
    userId: string,
    token: string,
  ): Promise<{ success: boolean; message: string }> {
    try {
      // Kiểm tra user tồn tại
      const user = await this.userManagerService.findOne(userId);
      if (!user) {
        throw new BadRequestException('User not found');
      }

      // Kiểm tra user có MFA không
      if (!user.useMfaSecret) {
        throw new BadRequestException('MFA is not enabled for this user');
      }

      // Verify token
      const isValid = this.verifyToken(user.mfaSecret!, token);
      if (!isValid) {
        throw new UnauthorizedException('Invalid MFA token');
      }

      // Disable MFA - chỉ set cờ useMfaSecret = false, giữ nguyên secret
      await this.userManagerService.update(userId, {
        useMfaSecret: false,
        mfaSecret: '',
      });

      this.logger.log(`Disabled MFA for user ${userId}`);

      return {
        success: true,
        message: 'MFA disabled successfully',
      };
    } catch (error) {
      this.logger.error('Error disabling MFA:', error);
      throw error;
    }
  }

  /**
   * Verify MFA token
   */
  async verifyMFA(userId: string, token: string): Promise<boolean> {
    try {
      // Kiểm tra user tồn tại
      const user = await this.userManagerService.findOne(userId);
      if (!user) {
        throw new BadRequestException('User not found');
      }

      // Kiểm tra user có MFA không
      if (!user.mfaSecret || !user.useMfaSecret) {
        throw new BadRequestException('MFA is not enabled for this user');
      }

      // Verify token
      const isValid = this.verifyToken(user.mfaSecret, token);

      this.logger.log(
        `MFA verification for user ${userId}: ${isValid ? 'SUCCESS' : 'FAILED'}`,
      );

      return isValid;
    } catch (error) {
      this.logger.error('Error verifying MFA:', error);
      throw error;
    }
  }

  /**
   * Save MFA secret cho user
   */
  async saveMFASecret(userId: string, secret: string): Promise<void> {
    try {
      await this.userManagerService.update(userId, {
        mfaSecret: secret,
      });

      this.logger.log(`Saved MFA secret for user ${userId}`);
    } catch (error) {
      this.logger.error('Error saving MFA secret:', error);
      throw error;
    }
  }

  /**
   * Verify TOTP token
   */
  private verifyToken(secret: string, token: string): boolean {
    return speakeasy.totp.verify({
      secret,
      encoding: 'base32',
      token,
      window: 2, // Cho phép 2 time steps trước và sau
    });
  }

  /**
   * Tạo backup codes
   */
  private generateBackupCodes(): string[] {
    const codes: string[] = [];
    for (let i = 0; i < 10; i++) {
      codes.push(speakeasy.generateSecret({ length: 10 }).base32);
    }
    return codes;
  }

  /**
   * Verify backup code
   */
  async verifyBackupCode(userId: string, backupCode: string): Promise<boolean> {
    try {
      // TODO: Implement backup codes verification
      // Cần lưu backup codes vào database và verify
      this.logger.log(`Backup code verification for user ${userId}`);
      return false; // Placeholder
    } catch (error) {
      this.logger.error('Error verifying backup code:', error);
      return false;
    }
  }

  /**
   * Get MFA status của user
   */
  async getMFAStatus(
    userId: string,
  ): Promise<{ enabled: boolean; secretExists: boolean }> {
    try {
      const user = await this.userManagerService.findOne(userId);
      if (!user) {
        throw new BadRequestException('User not found');
      }

      return {
        enabled: user.useMfaSecret || false,
        secretExists: !!user.mfaSecret,
      };
    } catch (error) {
      this.logger.error('Error getting MFA status:', error);
      throw error;
    }
  }
}
