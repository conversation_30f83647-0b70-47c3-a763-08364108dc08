// RabbitMQ constants for centralized management
export const RABBITMQ_EXCHANGES = {
  BRIGHT_TOPIC: 'bright.topic',
};

export const RABBITMQ_QUEUES = {
  AUDIT: 'bright.audit',
  MAIL_WELCOME: 'bright.mail.welcome',
  ERROR: 'bright.error',
  MAIL: 'bright.mail',
  // Thêm các queue khác nếu cần
};

export const RABBITMQ_ROUTING_KEYS = {
  AUDIT_USER: 'audit.user',
  AUDIT_AUDIT: 'audit.audit',
  MAIL_WELCOME: 'mail.welcome',
  MAIL_RESET: 'mail.reset',
  LOG_ERROR: 'log.error',
  // Thêm các routing key khác nếu cần
};
