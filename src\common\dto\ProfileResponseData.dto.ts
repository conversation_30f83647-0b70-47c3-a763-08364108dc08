import { ApiProperty } from '@nestjs/swagger';

export class ProfileResponseDataDto {
  @ApiProperty({
    description: 'Profile ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;
  @ApiProperty({ description: 'Full name', example: '<PERSON>' })
  full_name: string;
  @ApiProperty({
    description: 'Phone number',
    example: '+1234567890',
    required: false,
  })
  phone_number?: string;
  @ApiProperty({
    description: 'Avatar URL',
    example: 'https://example.com/avatar.jpg',
    required: false,
  })
  avatar_url?: string;
  @ApiProperty({
    description: 'Company name',
    example: 'Bright Company',
    required: false,
  })
  company?: string;
  @ApiProperty({
    description: 'Job title',
    example: 'Software Engineer',
    required: false,
  })
  occupation?: string;
  @ApiProperty({
    description: 'Bio',
    example: 'I am a software engineer...',
    required: false,
  })
  bio?: string;
  @ApiProperty({
    description: 'Address line 1',
    example: '123 Main St',
    required: false,
  })
  address1?: string;
  @ApiProperty({
    description: 'Address line 2',
    example: 'Apt 4B',
    required: false,
  })
  address2?: string;
  @ApiProperty({ description: 'City', example: 'New York', required: false })
  city?: string;
  @ApiProperty({
    description: 'State/Province',
    example: 'NY',
    required: false,
  })
  state?: string;
  @ApiProperty({
    description: 'ZIP/Postal code',
    example: '10001',
    required: false,
  })
  zipCode?: string;
  @ApiProperty({ description: 'Country', example: 'USA', required: false })
  country?: string;
  @ApiProperty({
    description: 'Gender',
    example: 'male',
    enum: ['male', 'female', 'other'],
    required: false,
  })
  gender?: string;
  @ApiProperty({
    description: 'Last login date',
    example: '2024-01-01T00:00:00.000Z',
    required: false,
  })
  lastLogin?: Date;
  @ApiProperty({
    description: 'Date of birth',
    example: '1990-01-01',
    required: false,
  })
  dateOfBirth?: Date;
  @ApiProperty({
    description: 'Avatar image URL',
    example: 'https://example.com/avatar.jpg',
    required: false,
  })
  avatarImage?: string;
  @ApiProperty({
    description: 'Cover image URL',
    example: 'https://example.com/cover.jpg',
    required: false,
  })
  coverImage?: string;
  @ApiProperty({
    description: 'Facebook profile URL',
    example: 'https://facebook.com/username',
    required: false,
  })
  facebookUrl?: string;
  @ApiProperty({
    description: 'Twitter profile URL',
    example: 'https://twitter.com/username',
    required: false,
  })
  twitterUrl?: string;
  @ApiProperty({
    description: 'LinkedIn profile URL',
    example: 'https://linkedin.com/in/username',
    required: false,
  })
  linkedinUrl?: string;
}
