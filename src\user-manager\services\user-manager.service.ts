import {
  Injectable,
  Logger,
  ConflictException,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import {
  CreateUserDto,
  CreateUserWithSettingsDto,
  UpdateUserDto,
} from '../dto/user.dto';
import { QuickCreateUserDto } from '../dto/quick-create.dto';
import { UserSettingsDto } from '../dto/user-settings.dto';

import { User } from '../entities/user.entity';
import { UserProfile } from '../entities/user-profile.entity';
import { UserSettings } from '../entities/user-settings.entity';
import { EmailQueue, EmailType } from '../entities/email-queue.entity';
import { FirebaseService } from './firebase.service';
import { UserSettingsService } from './user-settings.service';
import { EmailQueueService } from './email-queue.service';
import { PasswordService } from './password.service';
import { RedisService } from '../../common/services/redis.service';
import { MapperService } from '../../common/services/mapper.service';
import { Role } from 'src/common/enum/role.enum';
import * as crypto from 'crypto';
import { RabbitMQService } from '../../common/services/rabbitmq.service';
import { AdminUserFilterDto } from '../dto/admin-user.dto';
import { RoleEntity } from '../entities/role.entity';
import { Permission } from '../entities/permission.entity';
import {
  RABBITMQ_EXCHANGES,
  RABBITMQ_ROUTING_KEYS,
} from '../../common/enum/rabbitmq.enum';
import { RequestContext } from '../../common/context/request-context';
import { AuditLogService } from '../../common/services/audit-log.service';

@Injectable()
export class UserManagerService {
  private readonly logger = new Logger(UserManagerService.name);

  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(UserProfile)
    private userProfileRepository: Repository<UserProfile>,
    @InjectRepository(UserSettings)
    private userSettingsRepository: Repository<UserSettings>,
    @InjectRepository(EmailQueue)
    private emailQueueRepository: Repository<EmailQueue>,
    private firebaseService: FirebaseService,
    private userSettingsService: UserSettingsService,
    private emailQueueService: EmailQueueService,
    private passwordService: PasswordService,
    private redisService: RedisService,
    private mapperService: MapperService,
    private rabbitMQService: RabbitMQService,
    private requestContext: RequestContext,
    private auditLogService: AuditLogService,
  ) {}

  /**
   * Create a new user with Firebase and default settings
   */
  async createUserWithFirebase(createUserDto: CreateUserWithSettingsDto) {
    try {
      // Check if user already exists
      const existingUser = await this.userRepository.findOne({
        where: [
          { email: createUserDto.email },
          { firebaseId: createUserDto.firebaseId },
        ],
      });

      if (existingUser) {
        throw new ConflictException(
          'User already exists with this email or Firebase ID',
        );
      }

      // Verify Firebase user exists
      const firebaseUser = await this.firebaseService.getUserByUid(
        createUserDto.firebaseId,
      );

      // Create user in database
      const roleRepo = this.userRepository.manager.getRepository(RoleEntity);
      const role = await roleRepo.findOne({
        where: { name: createUserDto.role || 'user' },
      });
      if (!role) throw new BadRequestException('Role not found');
      const user = this.userRepository.create({
        email: createUserDto.email,
        firebaseId: createUserDto.firebaseId,
        role,
        is_active: true,
      });

      const savedUser = await this.userRepository.save(user);

      // Create user profile
      const userProfile = this.userProfileRepository.create({
        user: savedUser,
        full_name: firebaseUser.displayName || '',
        avatar_url: firebaseUser.photoURL || '',
        last_login: new Date(),
      });

      await this.userProfileRepository.save(userProfile);

      // Create default settings with custom values
      const customSettings = createUserDto.customSettings || {};

      const userSettings = await this.userSettingsService.createDefaultSettings(
        savedUser,
        // Chuyển Record<string, string> thành mảng object { key, value }
        Object.entries(customSettings).map(([key, value]) => ({
          key,
          value,
        })),
      );

      // Set custom claims in Firebase
      await this.firebaseService.setCustomClaims(createUserDto.firebaseId, {
        role: savedUser.role,
        userId: savedUser.id,
        email: savedUser.email,
      });

      // Tạo record welcome email và gửi message RabbitMQ
      const emailQueue = await this.emailQueueService.createWelcomeEmail(
        savedUser.email,
        userProfile.full_name,
        '', // Không có password vì user đã có trên Firebase
        process.env.LOGIN_URL || 'https://your-app.com/login',
      );
      await this.rabbitMQService.publish(
        RABBITMQ_EXCHANGES.BRIGHT_TOPIC,
        RABBITMQ_ROUTING_KEYS.MAIL_WELCOME,
        {
          id: emailQueue.id,
        },
      );

      this.logger.log(
        `Successfully created user with Firebase: ${savedUser.id}`,
      );

      return {
        user: savedUser,
        profile: userProfile,
        settings: userSettings,
        firebaseUser: {
          uid: firebaseUser.uid,
          email: firebaseUser.email,
          displayName: firebaseUser.displayName,
        },
      };
    } catch (error: unknown) {
      if (error instanceof Error) {
        this.logger.error('Error creating user with Firebase:', error);
      } else {
        this.logger.error('Error creating user with Firebase:', String(error));
      }
      throw error;
    }
  }

  /**
   * Create a new user (basic version)
   */
  async create(createUserDto: CreateUserDto) {
    try {
      // Check if user already exists
      const existingUser = await this.userRepository.findOne({
        where: [
          { email: createUserDto.email },
          { firebaseId: createUserDto.firebaseId },
        ],
      });

      if (existingUser) {
        throw new ConflictException(
          'User already exists with this email or Firebase ID',
        );
      }

      // Create user
      const roleRepo = this.userRepository.manager.getRepository(RoleEntity);
      const role = await roleRepo.findOne({
        where: { name: createUserDto.role || 'user' },
      });
      if (!role) throw new BadRequestException('Role not found');
      const user = this.userRepository.create({
        email: createUserDto.email,
        firebaseId: createUserDto.firebaseId,
        role,
        is_active: true,
      });

      const savedUser = await this.userRepository.save(user);

      // Create user profile
      const userProfile = this.userProfileRepository.create({
        user: savedUser,
        full_name: createUserDto.email.split('@')[0], // Use email prefix as fallback
        last_login: new Date(),
      });

      await this.userProfileRepository.save(userProfile);

      // Create default settings
      const userSettings =
        await this.userSettingsService.createDefaultSettings(savedUser);

      // Tạo record welcome email và gửi message RabbitMQ
      const emailQueue = await this.emailQueueService.createWelcomeEmail(
        savedUser.email,
        userProfile.full_name,
        '', // Không có password vì user đã có trên Firebase
        process.env.LOGIN_URL || 'https://your-app.com/login',
      );
      await this.rabbitMQService.publish(
        RABBITMQ_EXCHANGES.BRIGHT_TOPIC,
        RABBITMQ_ROUTING_KEYS.MAIL_WELCOME,
        {
          id: emailQueue.id,
        },
      );

      return {
        user: savedUser,
        profile: userProfile,
        settings: userSettings,
      };
    } catch (error: unknown) {
      if (error instanceof Error) {
        this.logger.error('Error creating user:', error);
      } else {
        this.logger.error('Error creating user:', String(error));
      }
      throw error;
    }
  }

  /**
   * Find all users
   */
  async findAll() {
    try {
      return await this.userRepository.find({
        relations: ['profile', 'settings'],
      });
    } catch (error: unknown) {
      if (error instanceof Error) {
        this.logger.error('Error finding all users:', error);
      } else {
        this.logger.error('Error finding all users:', String(error));
      }
      throw error;
    }
  }

  /**
   * Find user by ID (with 7-day cache)
   */
  async findOne(id: string) {
    try {
      // Try to get from cache first
      const cacheKey = this.redisService.generateUserKey(id);
      const cachedUser = await this.redisService.get<User>(cacheKey);

      if (cachedUser) {
        this.logger.log(`User ${id} retrieved from cache`);
        return cachedUser;
      }

      const user = await this.userRepository.findOne({
        where: { id },
        relations: ['profile', 'settings'],
      });

      if (!user) {
        throw new NotFoundException(`User with ID ${id} not found`);
      }

      // Cache the user for 7 days
      await this.redisService.set(cacheKey, user, { ttl: 604800 }); // 7 days

      this.logger.log(
        `User ${id} retrieved from database and cached for 7 days`,
      );
      return user;
    } catch (error: unknown) {
      if (error instanceof Error) {
        this.logger.error(`Error finding user ${id}:`, error);
      } else {
        this.logger.error(`Error finding user ${id}:`, String(error));
      }
      throw error;
    }
  }

  /**
   * Find user by Firebase ID (with 7-day cache)
   */
  async findByFirebaseId(firebaseId: string) {
    try {
      // Try to get from cache first
      const cacheKey =
        this.redisService.generateUserByFirebaseIdKey(firebaseId);
      const cachedUser = await this.redisService.get<User>(cacheKey);

      if (cachedUser) {
        this.logger.log(
          `User with Firebase ID ${firebaseId} retrieved from cache`,
        );
        return cachedUser;
      }

      const user = await this.userRepository.findOne({
        where: { firebaseId },
        relations: ['profile', 'settings'],
      });

      if (!user) {
        throw new NotFoundException(
          `User with Firebase ID ${firebaseId} not found`,
        );
      }

      // Cache the user for 7 days
      await this.redisService.set(cacheKey, user, { ttl: 604800 }); // 7 days

      this.logger.log(
        `User with Firebase ID ${firebaseId} retrieved from database and cached for 7 days`,
      );
      return user;
    } catch (error: unknown) {
      if (error instanceof Error) {
        this.logger.error(
          `Error finding user by Firebase ID ${firebaseId}:`,
          error,
        );
      } else {
        this.logger.error(
          `Error finding user by Firebase ID ${firebaseId}:`,
          String(error),
        );
      }
      throw error;
    }
  }

  /**
   * Update user
   */
  async update(id: string, updateUserDto: UpdateUserDto) {
    try {
      const user = await this.findOne(id);

      Object.assign(user, updateUserDto);

      const updatedUser = await this.userRepository.save(user);

      // Update Firebase custom claims if role changed
      if (updateUserDto.role) {
        await this.firebaseService.setCustomClaims(user.firebaseId, {
          role: updateUserDto.role,
          userId: user.id,
        });
      }

      // Invalidate all user-related cache
      await this.redisService.invalidateUserCache(id);

      this.logger.log(`User ${id} updated successfully`);
      return updatedUser;
    } catch (error: unknown) {
      if (error instanceof Error) {
        this.logger.error(`Error updating user ${id}:`, error);
      } else {
        this.logger.error(`Error updating user ${id}:`, String(error));
      }
      throw error;
    }
  }

  /**
   * Remove user
   */
  async remove(id: string) {
    try {
      const user = await this.findOne(id);

      // Delete from Firebase
      await this.firebaseService.deleteUser(user.firebaseId);

      // Delete from database
      await this.userRepository.remove(user);

      // Invalidate all user-related cache
      await this.redisService.invalidateUserCache(id);

      this.logger.log(`User ${id} deleted successfully`);
      return { success: true, message: 'User deleted successfully' };
    } catch (error: unknown) {
      if (error instanceof Error) {
        this.logger.error(`Error deleting user ${id}:`, error);
      } else {
        this.logger.error(`Error deleting user ${id}:`, String(error));
      }
      throw error;
    }
  }

  /**
   * Get user with all related data (with 7-day cache)
   */
  async getUserWithAllData(id: string) {
    try {
      // Try to get from cache first
      const cacheKey = this.redisService.generateUserKey(id);
      const cachedUser = await this.redisService.get<User>(cacheKey);

      if (cachedUser) {
        this.logger.log(`User with all data ${id} retrieved from cache`);
        return cachedUser;
      }

      const user = await this.userRepository.findOne({
        where: { id },
        relations: ['profile', 'settings'],
      });

      if (!user) {
        throw new NotFoundException(`User with ID ${id} not found`);
      }

      // Cache the user for 7 days
      await this.redisService.set(cacheKey, user, { ttl: 604800 }); // 7 days

      this.logger.log(
        `User with all data ${id} retrieved from database and cached for 7 days`,
      );
      return user;
    } catch (error: unknown) {
      if (error instanceof Error) {
        this.logger.error(`Error getting user with all data ${id}:`, error);
      } else {
        this.logger.error(
          `Error getting user with all data ${id}:`,
          String(error),
        );
      }
      throw error;
    }
  }

  /**
   * Update user settings
   */
  async updateUserSettings(userId: string, settings: Record<string, string>) {
    try {
      await this.userRepository.findOne({ where: { id: userId } }); // Verify user exists

      const updatedSettings =
        await this.userSettingsService.updateMultipleSettings(userId, settings);

      // Invalidate all user-related cache
      await this.redisService.invalidateUserCache(userId);

      this.logger.log(`User settings updated for user ${userId}`);
      // Map datatype field for all settings
      return updatedSettings.map((s) => ({
        id: s.id,
        key: s.setting_key,
        value: s.setting_value,
        datatype: s.datatype,
      }));
    } catch (error: unknown) {
      if (error instanceof Error) {
        this.logger.error(
          `Error updating user settings for user ${userId}:`,
          error,
        );
      } else {
        this.logger.error(
          `Error updating user settings for user ${userId}:`,
          String(error),
        );
      }
      throw error;
    }
  }

  /**
   * Get user settings (with 7-day cache)
   */
  async getUserSettings(userId: string) {
    try {
      // Try to get from cache first
      const cacheKey = this.redisService.generateUserSettingsKey(userId);
      const cachedSettings =
        await this.redisService.get<UserSettings[]>(cacheKey);

      if (cachedSettings) {
        this.logger.log(`User settings for ${userId} retrieved from cache`);
        // Map datatype field for all settings
        return cachedSettings.map((s) => ({
          id: s.id,
          key: s.setting_key,
          value: s.setting_value,
          datatype: s.datatype,
        }));
      }

      await this.userRepository.findOne({ where: { id: userId } });

      const settings = await this.userSettingsService.getUserSettings(userId);

      // Cache the settings for 7 days
      await this.redisService.set(cacheKey, settings, { ttl: 604800 }); // 7 days

      this.logger.log(
        `User settings for ${userId} retrieved from database and cached for 7 days`,
      );
      // Map datatype field for all settings
      return settings.map((s) => ({
        id: s.id,
        key: s.setting_key,
        value: s.setting_value,
        datatype: s.datatype,
      }));
    } catch (error: unknown) {
      if (error instanceof Error) {
        this.logger.error(
          `Error getting user settings for user ${userId}:`,
          error,
        );
      } else {
        this.logger.error(
          `Error getting user settings for user ${userId}:`,
          String(error),
        );
      }
      throw error;
    }
  }

  /**
   * Quick create user với auto password generation
   */
  async quickCreateUser(quickCreateUserDto: QuickCreateUserDto) {
    try {
      // Check if user already exists
      const existingUser = await this.userRepository.findOne({
        where: { email: quickCreateUserDto.email },
      });

      if (existingUser) {
        throw new ConflictException('User already exists with this email');
      }

      // Generate password
      const generatedPassword = this.passwordService.generatePassword();

      // Create Firebase user
      const firebaseUser = await this.firebaseService.createUser({
        email: quickCreateUserDto.email,
        password: generatedPassword,
        displayName: quickCreateUserDto.fullName,
      });

      // Create user in database
      const roleRepo = this.userRepository.manager.getRepository(RoleEntity);
      const role = await roleRepo.findOne({
        where: { name: quickCreateUserDto.role || 'user' },
      });
      if (!role) throw new BadRequestException('Role not found');
      const user = this.userRepository.create({
        email: quickCreateUserDto.email,
        firebaseId: firebaseUser.uid,
        role,
        is_active: true,
      });

      const savedUser = await this.userRepository.save(user);

      // Create user profile
      const userProfile = this.userProfileRepository.create({
        user: savedUser,
        full_name: quickCreateUserDto.fullName,
        phone_number: quickCreateUserDto.phone,
        avatar_url: quickCreateUserDto.avatar,
        company: quickCreateUserDto.company,
        occupation: quickCreateUserDto.jobTitle,
      });

      await this.userProfileRepository.save(userProfile);

      // Create default settings
      const userSettings =
        await this.userSettingsService.createDefaultSettings(savedUser);

      // Set custom claims in Firebase
      await this.firebaseService.setCustomClaims(firebaseUser.uid, {
        role: quickCreateUserDto.role || Role.USER,
        userId: savedUser.id,
      });

      // Create email queue entry (luôn gửi welcome email)
      const emailQueue = await this.emailQueueService.createWelcomeEmail(
        quickCreateUserDto.email,
        quickCreateUserDto.fullName,
        generatedPassword,
        process.env.LOGIN_URL || 'https://your-app.com/login',
      );
      await this.rabbitMQService.publish(
        RABBITMQ_EXCHANGES.BRIGHT_TOPIC,
        RABBITMQ_ROUTING_KEYS.MAIL_WELCOME,
        {
          id: emailQueue.id,
        },
      );

      this.logger.log(
        `Quick created user: ${savedUser.id} with Firebase UID: ${firebaseUser.uid}`,
      );

      return this.mapperService.createQuickCreateUserResponse(
        savedUser,
        userProfile,
        userSettings,
      );
    } catch (error: unknown) {
      if (error instanceof Error) {
        this.logger.error('Error in quick create user:', error);
      } else {
        this.logger.error('Error in quick create user:', String(error));
      }

      // Rollback Firebase user if database creation fails
      if (
        error instanceof Error &&
        error.message.includes('User already exists') &&
        error instanceof Error &&
        !error.message.includes('Firebase')
      ) {
        // Try to delete Firebase user if it was created
        try {
          // Add Firebase user deletion logic here if needed
        } catch (rollbackError: unknown) {
          if (rollbackError instanceof Error) {
            this.logger.error(
              'Error rolling back Firebase user:',
              rollbackError,
            );
          }
        }
      }

      throw error;
    }
  }

  /**
   * Bulk create users
   */
  async bulkCreateUsers(usersData: QuickCreateUserDto[]) {
    try {
      const results: any[] = [];
      const errors: any[] = [];

      for (const userData of usersData) {
        try {
          const result = await this.quickCreateUser(userData);
          results.push({
            email: userData.email,
            success: true,
            data: result,
          });
        } catch (error: unknown) {
          if (error instanceof Error) {
            errors.push({
              email: userData.email,
              success: false,
              error: error.message,
            });
          } else {
            errors.push({
              email: userData.email,
              success: false,
              error: String(error),
            });
          }
        }
      }

      this.logger.log(
        `Bulk create completed: ${results.length} successful, ${errors.length} failed`,
      );

      return this.mapperService.createBulkCreateResponse(
        usersData.length,
        results.length,
        errors.length,
        results,
        errors,
      );
    } catch (error: unknown) {
      if (error instanceof Error) {
        this.logger.error('Error in bulk create users:', error);
      } else {
        this.logger.error('Error in bulk create users:', String(error));
      }
      throw error;
    }
  }

  /**
   * Update user setting by key
   */
  async updateUserSettingByKey(
    userId: string,
    key: string,
    value: string,
  ): Promise<UserSettingsDto> {
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user) throw new NotFoundException('User not found');

    let setting = await this.userSettingsRepository.findOne({
      where: { setting_key: key, user: { id: userId } },
      relations: ['user'],
    });

    if (!setting) {
      setting = this.userSettingsRepository.create({
        user: user,
        setting_key: key,
        setting_value: value,
      });
    } else {
      setting.setting_value = value;
    }

    const saved = await this.userSettingsRepository.save(setting);
    // Convert to DTO if needed, else return saved
    return {
      id: saved.id,
      key: saved.setting_key,
      value: saved.setting_value,
      datatype: saved.datatype,
    };
  }

  async resetPasswordByEmail(
    email: string,
  ): Promise<{ success: boolean; message: string }> {
    // 1. Tìm user trong DB
    const user = await this.userRepository.findOne({ where: { email } });
    if (!user || !user.firebaseId) {
      throw new NotFoundException('User not found');
    }

    // 2. Kiểm tra user tồn tại trên Firebase
    let firebaseUser;
    try {
      firebaseUser = await this.firebaseService.getUserByUid(user.firebaseId);
    } catch (e: unknown) {
      if (e instanceof Error) {
        throw new NotFoundException('User not found in Firebase');
      }
    }

    // 3. Generate mật khẩu mới
    const newPassword = crypto.randomBytes(8).toString('base64');

    // 4. Đổi mật khẩu trên Firebase
    await (global as any).admin
      .auth()
      .updateUser(user.firebaseId, { password: newPassword });

    // 5. Lưu vào email_queue
    const subject = 'Mật khẩu mới cho tài khoản của bạn';
    const content = `Mật khẩu mới của bạn là: ${newPassword}`;
    const emailQueue = await this.emailQueueService.createEmailQueue({
      to_email: email,
      subject,
      content,
      email_type: EmailType.PASSWORD_RESET,
      metadata: { newPassword },
    });

    // 6. Gửi message tới RabbitMQ (chỉ gửi id)
    await this.rabbitMQService.publish(
      RABBITMQ_EXCHANGES.BRIGHT_TOPIC,
      RABBITMQ_ROUTING_KEYS.MAIL_RESET,
      {
        id: emailQueue.id,
      },
    );

    return { success: true, message: 'Password reset and email queued' };
  }

  async changePassword(
    email: string,
    oldPassword: string,
    newPassword: string,
    ip?: string,
    device?: string,
  ): Promise<{ success: boolean; message: string }> {
    if (!email) throw new BadRequestException('Missing user email');
    // 1. Lấy firebaseId từ DB
    const user = await this.userRepository.findOne({ where: { email } });
    if (!user || !user.firebaseId)
      throw new BadRequestException('User not found');

    // 2. Xác thực mật khẩu cũ qua Firebase REST API
    const isValid = await this.firebaseService.verifyPasswordWithFirebase(
      email,
      oldPassword,
    );
    if (!isValid) throw new BadRequestException('Old password is incorrect');

    // 3. Đổi mật khẩu mới trên Firebase
    await (global as any).admin
      .auth()
      .updateUser(user.firebaseId, { password: newPassword });

    // 4. Gửi email thông báo
    const subject = 'Bạn đã đổi mật khẩu thành công';
    const content =
      'Mật khẩu của bạn đã được thay đổi thành công. Nếu bạn không thực hiện thay đổi này, vui lòng liên hệ hỗ trợ ngay lập tức.';
    await this.emailQueueService.createEmailQueue({
      to_email: email,
      subject,
      content,
      email_type: EmailType.NOTIFICATION,
      metadata: { type: 'change_password' },
    });

    // 5. Gửi audit log qua RabbitMQ
    await this.auditLogService.sendUserAuditLog({
      userId: user.id,
      action: 'CHANGE_PASSWORD',
      meta: null,
    });

    return { success: true, message: 'Password changed and notification sent' };
  }

  // ADMIN: List users with filter, paging (nâng cấp filter)
  async adminListUsers(query: any) {
    const {
      page = 1,
      limit = 20,
      search,
      role,
      roles,
      status,
      is_email_verified,
      createdFrom,
      createdTo,
    } = query;
    const qb = this.userRepository
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.profile', 'profile')
      .leftJoinAndSelect('user.role', 'role');
    if (search) {
      qb.andWhere(
        'user.email ILIKE :search OR profile.full_name ILIKE :search',
        { search: `%${search}%` },
      );
    }
    if (role) {
      qb.andWhere('role.name = :role', { role });
    }
    if (roles && Array.isArray(roles) && roles.length > 0) {
      qb.andWhere('role.name IN (:...roles)', { roles });
    }
    if (status) {
      qb.andWhere('user.is_active = :status', { status: status === 'active' });
    }
    if (typeof is_email_verified === 'boolean') {
      qb.andWhere('user.is_email_verified = :is_email_verified', {
        is_email_verified,
      });
    }
    if (createdFrom) {
      qb.andWhere('user.created_at >= :createdFrom', { createdFrom });
    }
    if (createdTo) {
      qb.andWhere('user.created_at <= :createdTo', { createdTo });
    }
    qb.skip((page - 1) * limit)
      .take(limit)
      .orderBy('user.created_at', 'DESC');
    const [data, total] = await qb.getManyAndCount();
    return { data, total, page: +page, limit: +limit };
  }

  // ADMIN: User statistics
  async adminUserStats() {
    const total = await this.userRepository.count();
    const active = await this.userRepository.count({
      where: { is_active: true },
    });
    const locked = await this.userRepository.count({
      where: { is_active: false },
    });
    return { total, active, locked };
  }

  // ADMIN: Update user
  async adminUpdateUser(id: string, dto: any) {
    const user = await this.userRepository.findOne({ where: { id } });
    if (!user) throw new NotFoundException('User not found');
    Object.assign(user, dto);
    return this.userRepository.save(user);
  }

  // ADMIN: Lock user
  async adminLockUser(id: string) {
    const user = await this.userRepository.findOne({ where: { id } });
    if (!user) throw new NotFoundException('User not found');
    user.is_active = false;
    return this.userRepository.save(user);
  }

  // ADMIN: Unlock user
  async adminUnlockUser(id: string) {
    const user = await this.userRepository.findOne({ where: { id } });
    if (!user) throw new NotFoundException('User not found');
    user.is_active = true;
    return this.userRepository.save(user);
  }

  // ADMIN: Delete user
  async adminDeleteUser(id: string) {
    const user = await this.userRepository.findOne({ where: { id } });
    if (!user) throw new NotFoundException('User not found');
    await this.userRepository.remove(user);
    return { success: true };
  }

  // ADMIN: Export users (CSV, Excel) - tối ưu CSV streaming nếu có res
  async adminExportUsers(query: AdminUserFilterDto, res?: any) {
    const { export: exportType, ...filter } = query;
    const { data } = await this.adminListUsers(filter);
    if (exportType === 'csv') {
      // Nếu có res (response), dùng stream
      if (res && res.write) {
        const { stringify } = await import('csv-stringify');
        const stringifier = stringify({ header: true });
        res.setHeader('Content-Type', 'text/csv');
        res.setHeader(
          'Content-Disposition',
          'attachment; filename="users.csv"',
        );
        stringifier.pipe(res);
        data.forEach((u) =>
          stringifier.write({
            id: u.id,
            email: u.email,
            full_name: u.profile?.full_name,
            role: u.role?.name,
            is_active: u.is_active,
            created_at: u.created_at,
          }),
        );
        stringifier.end();
        return;
      } else {
        const { stringify } = await import('csv-stringify/sync');
        const csv = stringify(
          data.map((u) => ({
            id: u.id,
            email: u.email,
            full_name: u.profile?.full_name,
            role: u.role?.name,
            is_active: u.is_active,
            created_at: u.created_at,
          })),
          { header: true },
        );
        return {
          contentType: 'text/csv',
          filename: 'users.csv',
          buffer: Buffer.from(csv, 'utf-8'),
        };
      }
    } else if (exportType === 'excel') {
      const ExcelJS = await import('exceljs');
      const workbook = new ExcelJS.Workbook();
      const sheet = workbook.addWorksheet('Users');
      sheet.columns = [
        { header: 'ID', key: 'id' },
        { header: 'Email', key: 'email' },
        { header: 'Full Name', key: 'full_name' },
        { header: 'Role', key: 'role' },
        { header: 'Active', key: 'is_active' },
        { header: 'Created At', key: 'created_at' },
      ];
      data.forEach((u) =>
        sheet.addRow({
          id: u.id,
          email: u.email,
          full_name: u.profile?.full_name,
          role: u.role?.name,
          is_active: u.is_active,
          created_at: u.created_at,
        }),
      );
      const buffer = await workbook.xlsx.writeBuffer();
      return {
        contentType:
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        filename: 'users.xlsx',
        buffer,
      };
    }
    throw new Error('Invalid export type');
  }

  // ADMIN: Lịch sử hoạt động user (audit log)
  async adminUserAuditLog(userId: string, { limit = 20, page = 1 }) {
    // Giả sử bạn có entity AuditLog và repository
    const qb = this.userRepository.manager
      .getRepository('AuditLog')
      .createQueryBuilder('log');
    qb.where('log.user_id = :userId', { userId })
      .orderBy('log.created_at', 'DESC')
      .skip((page - 1) * limit)
      .take(limit);
    const [data, total] = await qb.getManyAndCount();
    return { data, total, page, limit };
  }

  // ADMIN: Export audit log toàn hệ thống
  async adminExportAuditLog(query: any, res?: any) {
    const { export: exportType, userId, action, from, to } = query;
    const qb = this.userRepository.manager
      .getRepository('AuditLog')
      .createQueryBuilder('log');
    if (userId) qb.andWhere('log.user_id = :userId', { userId });
    if (action) qb.andWhere('log.action = :action', { action });
    if (from) qb.andWhere('log.created_at >= :from', { from });
    if (to) qb.andWhere('log.created_at <= :to', { to });
    qb.orderBy('log.created_at', 'DESC');
    const data = await qb.getMany();
    if (exportType === 'csv') {
      if (res && res.write) {
        const { stringify } = await import('csv-stringify');
        const stringifier = stringify({ header: true });
        res.setHeader('Content-Type', 'text/csv');
        res.setHeader(
          'Content-Disposition',
          'attachment; filename="audit-log.csv"',
        );
        stringifier.pipe(res);
        data.forEach((l) => stringifier.write(l));
        stringifier.end();
        return;
      } else {
        const { stringify } = await import('csv-stringify/sync');
        const csv = stringify(data, {
          header: true,
        });
        return {
          contentType: 'text/csv',
          filename: 'audit-log.csv',
          buffer: Buffer.from(csv, 'utf-8'),
        };
      }
    } else if (exportType === 'excel') {
      const ExcelJS = await import('exceljs');
      const workbook = new ExcelJS.Workbook();
      const sheet = workbook.addWorksheet('AuditLog');
      if (data.length > 0) {
        sheet.columns = Object.keys(data[0]).map((k) => ({
          header: k,
          key: k,
        }));
        data.forEach((l) => sheet.addRow(l));
      }
      const buffer = await workbook.xlsx.writeBuffer();
      return {
        contentType:
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        filename: 'audit-log.xlsx',
        buffer,
      };
    }
    throw new Error('Invalid export type');
  }

  // ADMIN: Role CRUD
  async adminListRoles() {
    return this.userRepository.manager
      .getRepository(RoleEntity)
      .find({ relations: ['permissions'] });
  }
  async adminCreateRole(dto: { name: string; description?: string }) {
    const repo = this.userRepository.manager.getRepository(RoleEntity);
    const role = repo.create(dto);
    return repo.save(role);
  }
  async adminUpdateRole(id: string, dto: any) {
    const repo = this.userRepository.manager.getRepository(RoleEntity);
    const role = await repo.findOne({ where: { id } });
    if (!role) throw new NotFoundException('Role not found');
    Object.assign(role, dto);
    return repo.save(role);
  }
  async adminDeleteRole(id: string) {
    const repo = this.userRepository.manager.getRepository(RoleEntity);
    const role = await repo.findOne({ where: { id } });
    if (!role) throw new NotFoundException('Role not found');
    await repo.remove(role);
    return { success: true };
  }
  async adminGetRolePermissions(id: string) {
    const repo = this.userRepository.manager.getRepository(RoleEntity);
    const role = await repo.findOne({
      where: { id },
      relations: ['permissions'],
    });
    if (!role) throw new NotFoundException('Role not found');
    return role.permissions;
  }
  async adminAddPermissionsToRole(id: string, permissionIds: string[]) {
    const repo = this.userRepository.manager.getRepository(RoleEntity);
    const permRepo = this.userRepository.manager.getRepository(Permission);
    const role = await repo.findOne({
      where: { id },
      relations: ['permissions'],
    });
    if (!role) throw new NotFoundException('Role not found');
    const permissions = await permRepo.findByIds(permissionIds);
    role.permissions = Array.from(
      new Set([...(role.permissions || []), ...permissions]),
    );
    return repo.save(role);
  }
  async adminRemovePermissionsFromRole(id: string, permissionIds: string[]) {
    const repo = this.userRepository.manager.getRepository(RoleEntity);
    const role = await repo.findOne({
      where: { id },
      relations: ['permissions'],
    });
    if (!role) throw new NotFoundException('Role not found');
    role.permissions = (role.permissions || []).filter(
      (p) => !permissionIds.includes(p.id),
    );
    return repo.save(role);
  }

  // ADMIN: Permission CRUD
  async adminListPermissions() {
    return this.userRepository.manager.getRepository(Permission).find();
  }
  async adminCreatePermission(dto: { name: string; description?: string }) {
    const repo = this.userRepository.manager.getRepository(Permission);
    const perm = repo.create(dto);
    return repo.save(perm);
  }
  async adminUpdatePermission(id: string, dto: any) {
    const repo = this.userRepository.manager.getRepository(Permission);
    const perm = await repo.findOne({ where: { id } });
    if (!perm) throw new NotFoundException('Permission not found');
    Object.assign(perm, dto);
    return repo.save(perm);
  }
  async adminDeletePermission(id: string) {
    const repo = this.userRepository.manager.getRepository(Permission);
    const perm = await repo.findOne({ where: { id } });
    if (!perm) throw new NotFoundException('Permission not found');
    await repo.remove(perm);
    return { success: true };
  }

  // ADMIN: Gán role cho user (không còn dùng bảng liên kết)
  // Xóa các hàm adminGetUserRoles, adminAddRolesToUser, adminRemoveRolesFromUser, adminGetUserPermissions

  // ADMIN: Delete all users in Firebase
  async adminDeleteAllFirebaseUsers() {
    // Lấy toàn bộ user từ Firebase
    const firebaseUsers = await this.firebaseService.listAllUsers();
    let deleted = 0;
    const errors: any[] = [];
    for (const user of firebaseUsers) {
      try {
        await this.firebaseService.deleteUser(user.uid);
        deleted++;
      } catch (e: unknown) {
        if (e instanceof Error) {
          errors.push({ uid: user.uid, error: e.message });
        }
      }
    }
    // Nếu muốn xoá luôn trên DB:
    // await this.userRepository.clear();
    return { success: true, deleted, errors };
  }

  /**
   * Delete all users in both Firebase and the local database (no auth required)
   */
  async deleteAllUsersEverywhere() {
    // Delete all users from Firebase
    const firebaseUsers = await this.firebaseService.listAllUsers();
    let deletedFirebase = 0;
    const firebaseErrors: any[] = [];
    for (const user of firebaseUsers) {
      try {
        await this.firebaseService.deleteUser(user.uid);
        deletedFirebase++;
      } catch (e: unknown) {
        if (e instanceof Error) {
          firebaseErrors.push({ uid: user.uid, error: e.message });
        }
      }
    }
    // Delete all users from the local database (delete in dependency order, fix for TypeORM)
    await this.emailQueueRepository.createQueryBuilder().delete().execute();
    await this.userSettingsRepository.createQueryBuilder().delete().execute();
    await this.userProfileRepository.createQueryBuilder().delete().execute();
    await this.userRepository.createQueryBuilder().delete().execute();
    return {
      success: true,
      deletedFirebase,
      firebaseErrors,
      deletedLocal: true,
    };
  }
}
