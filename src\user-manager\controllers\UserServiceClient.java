// Example Java integration for Chat Service to call User Service APIs
// This shows how to update your existing UserServiceClient to use the new APIs

package com.bright.chat.service;

import com.bright.chat.model.UserInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.List;
import java.util.Optional;

/**
 * Updated Client service for communicating with User Service using new Chat APIs
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class UserServiceClient {

    private final RestTemplate restTemplate;

    @Value("${user-service.base-url:http://localhost:3000}")
    private String userServiceBaseUrl;

    @Value("${user-service.api-key}")
    private String apiKey;

    /**
     * Create HTTP headers with API key authentication
     */
    private HttpHeaders createHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.set("X-API-Key", apiKey);
        headers.set("X-Service-Name", "chat-service");
        headers.set("X-Service-Version", "1.0.0");
        headers.set("Content-Type", "application/json");
        return headers;
    }

    /**
     * Get user information by user ID with Redis caching
     */
    @Cacheable(value = "users", key = "#userId", unless = "#result.isEmpty()")
    public Optional<UserInfo> getUserById(String userId) {
        try {
            log.debug("Fetching user from User Service: {}", userId);
            String url = userServiceBaseUrl + "/api/users/" + userId;

            HttpEntity<?> entity = new HttpEntity<>(createHeaders());
            ResponseEntity<UserInfo> response = restTemplate.exchange(
                url, 
                HttpMethod.GET, 
                entity, 
                UserInfo.class
            );

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                log.debug("User found and cached: {}", userId);
                return Optional.of(response.getBody());
            }

            log.warn("User not found with ID: {}", userId);
            return Optional.empty();
        } catch (Exception e) {
            log.error("Error fetching user with ID: {}", userId, e);
            return Optional.empty();
        }
    }

    /**
     * Get multiple users by their IDs with caching
     */
    @Cacheable(value = "usersBatch", key = "#userIds.hashCode()", unless = "#result.isEmpty()")
    public List<UserInfo> getUsersByIds(List<String> userIds) {
        try {
            log.debug("Fetching {} users from User Service", userIds.size());
            String url = userServiceBaseUrl + "/api/users/batch";

            // Create request body
            BatchUserRequest request = new BatchUserRequest();
            request.setUserIds(userIds);

            HttpEntity<BatchUserRequest> entity = new HttpEntity<>(request, createHeaders());
            ResponseEntity<List<UserInfo>> response = restTemplate.exchange(
                url,
                HttpMethod.POST,
                entity,
                new ParameterizedTypeReference<List<UserInfo>>() {}
            );

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                log.debug("Fetched and cached {} users", response.getBody().size());
                return response.getBody();
            }

            log.warn("Failed to fetch users for IDs: {}", userIds);
            return List.of();
        } catch (Exception e) {
            log.error("Error fetching users with IDs: {}", userIds, e);
            return List.of();
        }
    }

    /**
     * Search users by username or display name with caching
     */
    @Cacheable(value = "userSearch", key = "#query + '_' + #limit", unless = "#result.isEmpty()")
    public List<UserInfo> searchUsers(String query, int limit) {
        try {
            log.debug("Searching users with query: {} (limit: {})", query, limit);
            String url = userServiceBaseUrl + "/api/users?q=" + query + "&limit=" + limit;

            HttpEntity<?> entity = new HttpEntity<>(createHeaders());
            ResponseEntity<List<UserInfo>> response = restTemplate.exchange(
                url,
                HttpMethod.GET,
                entity,
                new ParameterizedTypeReference<List<UserInfo>>() {}
            );

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                log.debug("Found and cached {} users for query: {}", response.getBody().size(), query);
                return response.getBody();
            }

            return List.of();
        } catch (Exception e) {
            log.error("Error searching users with query: {}", query, e);
            return List.of();
        }
    }

    /**
     * Check if user exists with caching
     */
    @Cacheable(value = "userValidation", key = "'exists_' + #userId")
    public boolean userExists(String userId) {
        try {
            log.debug("Checking if user exists: {}", userId);
            String url = userServiceBaseUrl + "/api/users/" + userId + "/exists";
            
            HttpEntity<?> entity = new HttpEntity<>(createHeaders());
            ResponseEntity<Boolean> response = restTemplate.exchange(
                url, 
                HttpMethod.GET, 
                entity, 
                Boolean.class
            );

            boolean exists = response.getStatusCode().is2xxSuccessful() && 
                           response.getBody() != null && 
                           response.getBody();

            log.debug("User {} exists: {}", userId, exists);
            return exists;
        } catch (Exception e) {
            log.error("Error checking if user exists with ID: {}", userId, e);
            return false;
        }
    }

    /**
     * Validate multiple user IDs with caching
     */
    @Cacheable(value = "userValidation", key = "'validate_' + #userIds.hashCode()")
    public List<String> validateUserIds(List<String> userIds) {
        try {
            log.debug("Validating {} user IDs", userIds.size());
            String url = userServiceBaseUrl + "/api/users/validate";

            BatchUserRequest request = new BatchUserRequest();
            request.setUserIds(userIds);

            HttpEntity<BatchUserRequest> entity = new HttpEntity<>(request, createHeaders());
            ResponseEntity<List<String>> response = restTemplate.exchange(
                url,
                HttpMethod.POST,
                entity,
                new ParameterizedTypeReference<List<String>>() {}
            );

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                log.debug("Validated {} out of {} user IDs", response.getBody().size(), userIds.size());
                return response.getBody();
            }

            return List.of();
        } catch (Exception e) {
            log.error("Error validating user IDs: {}", userIds, e);
            return List.of();
        }
    }

    /**
     * Get user role from User Service
     */
    @Cacheable(value = "userRoles", key = "#userId", unless = "#result == null")
    public String getUserRole(String userId) {
        try {
            log.debug("Fetching user role from User Service: {}", userId);
            String url = userServiceBaseUrl + "/api/users/" + userId + "/role";

            HttpEntity<?> entity = new HttpEntity<>(createHeaders());
            ResponseEntity<String> response = restTemplate.exchange(
                url, 
                HttpMethod.GET, 
                entity, 
                String.class
            );

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                log.debug("User role found and cached: {} -> {}", userId, response.getBody());
                return response.getBody();
            }

            log.warn("No role found for user: {}", userId);
            return "USER"; // Default role
        } catch (Exception e) {
            log.error("Error fetching user role for ID: {}", userId, e);
            return "USER"; // Default role on error
        }
    }

    /**
     * Get user profile with role
     */
    @Cacheable(value = "userProfiles", key = "#userId", unless = "#result.isEmpty()")
    public Optional<UserProfile> getUserProfile(String userId) {
        try {
            log.debug("Fetching user profile from User Service: {}", userId);
            String url = userServiceBaseUrl + "/api/users/" + userId + "/profile";

            HttpEntity<?> entity = new HttpEntity<>(createHeaders());
            ResponseEntity<UserProfile> response = restTemplate.exchange(
                url, 
                HttpMethod.GET, 
                entity, 
                UserProfile.class
            );

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                log.debug("User profile found and cached: {}", userId);
                return Optional.of(response.getBody());
            }

            log.warn("User profile not found with ID: {}", userId);
            return Optional.empty();
        } catch (Exception e) {
            log.error("Error fetching user profile with ID: {}", userId, e);
            return Optional.empty();
        }
    }

    // ... rest of the cache eviction methods remain the same

    /**
     * Request DTO for batch operations
     */
    public static class BatchUserRequest {
        private List<String> userIds;

        public List<String> getUserIds() { return userIds; }
        public void setUserIds(List<String> userIds) { this.userIds = userIds; }
    }
}
