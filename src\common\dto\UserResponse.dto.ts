import { ApiProperty } from '@nestjs/swagger';

export class UserResponseDto {
  @ApiProperty({
    description: 'User ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  id: string;

  @ApiProperty({ description: 'User email', example: '<EMAIL>' })
  email: string;

  @ApiProperty({ description: 'Firebase ID', example: 'firebase_uid_here' })
  firebaseId: string;

  @ApiProperty({ description: 'User role', example: 'USER' })
  role: string;

  @ApiProperty({ description: 'User active status', example: true })
  is_active: boolean;

  @ApiProperty({ description: 'MFA enabled status', example: false })
  useMfaSecret: boolean;
}
