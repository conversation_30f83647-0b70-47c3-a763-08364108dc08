import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AuthService } from './services/auth.service';
import { AuthController } from './controllers/auth.controller';
import { MFAService } from './services/mfa.service';
import { MFAController } from './controllers/mfa.controller';
import { TokenService } from './services/token.service';
import { TokenController } from './controllers/token.controller';
import { FirebaseAuthGuard, RolesGuard } from './guards/auth.guards';
import { UserManagerModule } from '../user-manager/user-manager.module';
import { AdminSeeder } from '../common/seeders/admin.seeder';
import { SeederController } from '../common/controllers/seeder.controller';
import { AdminTokenService } from '../common/services/admin-token.service';
import { AdminTokenController } from '../common/controllers/admin-token.controller';
import { CacheController } from '../common/controllers/cache.controller';
import { User } from '../user-manager/entities/user.entity';
import { UserProfile } from '../user-manager/entities/user-profile.entity';
import { UserSettings } from '../user-manager/entities/user-settings.entity';
import { EmailQueue } from '../user-manager/entities/email-queue.entity';
import { RedisModule } from '../common/modules/redis.module';
import { MapperModule } from '../common/modules/mapper.module';
import { RabbitMQModule } from '../common/modules/rabbitmq.module';
import { CommonModule } from 'src/common/common.module';
import { RabbitMQService } from '../common/services/rabbitmq.service';

@Module({
  imports: [
    CommonModule,
    TypeOrmModule.forFeature([User, UserProfile, UserSettings, EmailQueue]),
    RedisModule,
    MapperModule,
    UserManagerModule,
    RabbitMQModule,
  ],
  controllers: [
    AuthController,
    MFAController,
    TokenController,
    SeederController,
    AdminTokenController,
    CacheController,
  ],
  providers: [
    AuthService,
    MFAService,
    TokenService,
    AdminSeeder,
    AdminTokenService,
    FirebaseAuthGuard,
    RolesGuard,
    RabbitMQService,
  ],
  exports: [
    AuthService,
    MFAService,
    TokenService,
    AdminSeeder,
    AdminTokenService,
    FirebaseAuthGuard,
    RolesGuard,
    RabbitMQService,
  ],
})
export class AuthModule {}
