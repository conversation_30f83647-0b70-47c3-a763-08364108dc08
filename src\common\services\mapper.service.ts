import { Injectable } from '@nestjs/common';
import { User } from '../../user-manager/entities/user.entity';
import { UserProfile } from '../../user-manager/entities/user-profile.entity';
import { UserSettings } from '../../user-manager/entities/user-settings.entity';
import { UserDto } from '../../user-manager/dto/user.dto';
import { UserSettingsDto } from '../../user-manager/dto/user-settings.dto';
import {
  UserResponseDto,
  ProfileResponseDataDto,
  SettingsResponseDto,
  FirebaseUserResponseDto,
  EmailQueueResponseDto,
  QuickCreateUserResponseDto,
  BulkCreateResponseDto,
  AvatarUploadResponseDto,
  AccountStatusResponseDto,
  AdminSeedingResponseDto,
  AuthVerifyResponseDto,
  AuthTestResponseDto,
  TokenResponseDto,
  BaseResponseDto,
} from '../dto/response.dto';
import { UserProfileDto } from '../../user-manager/dto/UserProfile.dto';

@Injectable()
export class MapperService {
  /**
   * Map User entity to UserDto
   */
  mapUserToDto(user: User): UserDto {
    return {
      id: user.id, // Keep as string (UUID)
      email: user.email,
      role: user.role?.name ?? '', // Nếu muốn trả về mảng: roles: user.roles?.map(r => r.name) ?? []
      isActive: user.is_active,
    };
  }

  /**
   * Map UserProfile entity to UserProfileDto
   */
  mapUserProfileToDto(profile: UserProfile): UserProfileDto {
    return {
      full_name: profile.full_name,
      phone_number: profile.phone_number,
      company: profile.company,
      occupation: profile.occupation,
      bio: profile.bio,
      address1: profile.address1,
      address2: profile.address2,
      city: profile.city,
      state: profile.state,
      zipCode: profile.zipCode,
      country: profile.country,
      gender: profile.gender,
      dateOfBirth: profile.dateOfBirth,
    };
  }

  /**
   * Map UserSettings entity to UserSettingsDto
   */
  mapUserSettingToDto(setting: UserSettings): UserSettingsDto {
    return {
      id: setting.id, // Keep as string (UUID)
      key: setting.setting_key,
      value: setting.setting_value,
      description: '', // UserSettings entity doesn't have description field
    };
  }

  /**
   * Map array of User entities to UserDto array
   */
  mapUsersToDto(users: User[]): UserDto[] {
    return users.map((user) => this.mapUserToDto(user));
  }

  /**
   * Map array of UserProfile entities to UserProfileDto array
   */
  mapUserProfilesToDto(profiles: UserProfile[]): UserProfileDto[] {
    return profiles.map((profile) => this.mapUserProfileToDto(profile));
  }

  /**
   * Map array of UserSettings entities to UserSettingsDto array
   */
  mapUserSettingsToDto(settings: UserSettings[]): UserSettingsDto[] {
    return settings.map((setting) => this.mapUserSettingToDto(setting));
  }

  /**
   * Map User with Profile to combined DTO
   */
  mapUserWithProfileToDto(user: User, profile?: UserProfile) {
    return {
      user: this.mapUserToDto(user),
      profile: profile ? this.mapUserProfileToDto(profile) : null,
    };
  }

  /**
   * Map User with Profile and Settings to combined DTO
   */
  mapUserWithProfileAndSettingsToDto(
    user: User,
    profile?: UserProfile,
    settings?: UserSettings[],
  ) {
    return {
      user: this.mapUserToDto(user),
      profile: profile ? this.mapUserProfileToDto(profile) : null,
      settings: settings
        ? settings.map((s) => this.mapUserSettingToDto(s))
        : [],
    };
  }

  /**
   * Map UserSettings array to object format
   */
  mapUserSettingsToObject(settings: UserSettings[]): Record<string, string> {
    const settingsObject: Record<string, string> = {};

    settings.forEach((setting) => {
      settingsObject[setting.setting_key] = setting.setting_value;
    });

    return settingsObject;
  }

  /**
   * Create response DTO with success status
   */
  createSuccessResponse<T>(
    data: T,
    message: string = 'Success',
  ): BaseResponseDto<T> {
    return {
      success: true,
      message,
      data,
    };
  }

  /**
   * Create error response DTO
   */
  createErrorResponse(message: string, error?: any) {
    return {
      success: false,
      message,
      error: error?.message || error,
    };
  }

  // Response Mapping Methods

  /**
   * Map User entity to UserResponseDto
   */
  mapUserToResponseDto(user: User): UserResponseDto {
    return {
      id: user.id,
      email: user.email,
      firebaseId: user.firebaseId,
      role: user.role?.name ?? '',
      useMfaSecret: user.useMfaSecret || false,
      is_active: user.is_active,
    };
  }

  /**
   * Map UserProfile entity to ProfileResponseDataDto
   */
  mapUserProfileToResponseDto(profile: UserProfile): ProfileResponseDataDto {
    return {
      id: profile.id,
      full_name: profile.full_name,
      phone_number: profile.phone_number,
      avatar_url: profile.avatar_url,
      company: profile.company,
      occupation: profile.occupation,
      bio: profile.bio,
      address1: profile.address1,
      address2: profile.address2,
      city: profile.city,
      state: profile.state,
      zipCode: profile.zipCode,
      country: profile.country,
      gender: profile.gender,
      lastLogin: profile.last_login,
      dateOfBirth: profile.dateOfBirth,
      avatarImage: profile.avatarImage,
      coverImage: profile.coverImage,
      facebookUrl: profile.facebookUrl,
      twitterUrl: profile.twitterUrl,
      linkedinUrl: profile.linkedinUrl,
    };
  }

  /**
   * Map UserSettings entity to SettingsResponseDto
   */
  mapUserSettingsToResponseDto(setting: UserSettings): SettingsResponseDto {
    return {
      key: setting.setting_key,
      value: setting.setting_value,
      description: setting.description,
      datatype: setting.datatype,
    };
  }

  /**
   * Map UserSettings array to SettingsResponseDto array
   */
  mapUserSettingsArrayToResponseDto(
    settings: UserSettings[],
  ): SettingsResponseDto[] {
    return settings.map((setting) =>
      this.mapUserSettingsToResponseDto(setting),
    );
  }

  /**
   * Map Firebase user to FirebaseUserResponseDto
   */
  mapFirebaseUserToResponseDto(firebaseUser: any): FirebaseUserResponseDto {
    return {
      uid: firebaseUser.uid,
      email: firebaseUser.email,
      displayName: firebaseUser.displayName,
    };
  }

  /**
   * Map EmailQueue entity to EmailQueueResponseDto
   */
  mapEmailQueueToResponseDto(emailQueue: any): EmailQueueResponseDto {
    return {
      id: emailQueue.id,
      status: emailQueue.status,
      email_type: emailQueue.email_type,
    };
  }

  /**
   * Create Quick Create User Response (optimized - no duplicate data)
   */
  createQuickCreateUserResponse(
    user: User,
    profile: UserProfile,
    settings: UserSettings[],
  ): QuickCreateUserResponseDto {
    return {
      success: true,
      message:
        'User created successfully. Welcome email has been queued for sending.',
      data: {
        user: this.mapUserToResponseDto(user),
        profile: this.mapUserProfileToResponseDto(profile),
        settings: this.mapUserSettingsArrayToResponseDto(settings),
      },
    };
  }

  /**
   * Create Bulk Create Response
   */
  createBulkCreateResponse(
    total: number,
    successful: number,
    failed: number,
    results: any[],
    errors: any[],
  ): BulkCreateResponseDto {
    return {
      success: true,
      message: 'Bulk create completed',
      data: {
        total,
        successful,
        failed,
        results,
        errors,
      },
      total,
      successful,
      failed,
      results,
      errors,
    };
  }

  /**
   * Create Avatar Upload Response
   */
  createAvatarUploadResponse(
    avatarUrl: string,
    filename?: string,
    size?: number,
    mimetype?: string,
  ): AvatarUploadResponseDto {
    return {
      success: true,
      message: 'Avatar uploaded successfully',
      data: {
        avatarUrl,
        filename,
        size,
        mimetype,
      },
      avatarUrl,
      filename,
      size,
      mimetype,
    };
  }

  /**
   * Create Account Status Response
   */
  createAccountStatusResponse(
    isActive: boolean,
    message: string,
  ): AccountStatusResponseDto {
    return {
      success: true,
      message,
      data: {
        isActive,
      },
      isActive,
    };
  }

  /**
   * Create Admin Seeding Response
   */
  createAdminSeedingResponse(
    user: User,
    profile: UserProfile,
    settings: UserSettings[],
    firebaseUser: any,
  ): AdminSeedingResponseDto {
    return {
      success: true,
      message: 'Admin user created successfully',
      data: {
        user: this.mapUserToResponseDto(user),
        profile: this.mapUserProfileToResponseDto(profile),
        settings: this.mapUserSettingsArrayToResponseDto(settings),
        firebaseUser: this.mapFirebaseUserToResponseDto(firebaseUser),
      },
    };
  }

  /**
   * Create Auth Verify Response (optimized - using DTOs)
   */
  createAuthVerifyResponse(
    user: User,
    profile: any,
    settings: any[],
    firebase: any,
    token: string,
  ): AuthVerifyResponseDto {
    return {
      success: true,
      message: 'Token verified successfully',
      data: {
        user: this.mapUserToResponseDto(user),
        profile: profile ? this.mapUserProfileToResponseDto(profile) : null,
        settings: settings
          ? this.mapUserSettingsArrayToResponseDto(settings)
          : [],
        firebase,
        token,
      },
    };
  }

  /**
   * Create Auth Test Response
   */
  createAuthTestResponse(user: any): AuthTestResponseDto {
    return {
      success: true,
      message: 'Authentication successful',
      data: {
        user,
        timestamp: new Date().toISOString(),
      },
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Create Token Response
   */
  createTokenResponse(
    accessToken: string,
    tokenType: string,
    expiresIn: number,
  ): TokenResponseDto {
    return {
      accessToken,
      tokenType,
      expiresIn,
    };
  }

  /**
   * Create Admin Login Response
   */
  createAdminLoginResponse(tokenData: any, user: User): any {
    return {
      success: true,
      message: 'Admin login successful',
      data: {
        token: tokenData.token,
        expiresIn: tokenData.expiresIn,
        refreshToken: tokenData.refreshToken,
      },
      user: {
        id: user.id,
        email: user.email,
        role: user.role?.name ?? '',
        firebaseId: user.firebaseId,
      },
    };
  }
}
