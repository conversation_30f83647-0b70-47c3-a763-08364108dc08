import { ApiProperty } from '@nestjs/swagger';

export class SettingsResponseDto {
  @ApiProperty({ description: 'Setting key', example: 'theme' })
  key: string;

  @ApiProperty({ description: 'Setting value', example: 'dark' })
  value: string;

  @ApiProperty({
    description: 'Description of the setting',
    example: '<PERSON>iao diện sáng/tối',
    required: false,
  })
  description?: string;

  @ApiProperty({
    description: 'Data type of the setting',
    example: 'string',
    required: false,
  })
  datatype?: string;
}
