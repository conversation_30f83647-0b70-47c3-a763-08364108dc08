import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsEmail,
  IsOptional,
  IsEnum,
  IsBoolean,
} from 'class-validator';
import { Role } from '../../common/enum/role.enum';

export class UserDto {
  @ApiProperty({
    description: 'User ID (UUID)',
    example: '123e4567-e89b-12d3-a456-************',
  })
  id: string;

  @ApiProperty({
    description: 'User email',
    example: '<EMAIL>',
  })
  email: string;

  @ApiProperty({
    description: 'User role',
    example: 'USER',
    enum: ['USER', 'ADMIN'],
  })
  role: string;

  @ApiProperty({
    description: 'User active status',
    example: true,
  })
  isActive: boolean;
}

export class CreateUserDto {
  @ApiProperty({
    description: 'User email',
    example: '<EMAIL>',
  })
  @IsEmail({}, { message: '<PERSON>ail không hợp lệ' })
  email: string;

  @ApiProperty({
    description: 'Firebase ID của user',
    example: 'firebase_uid_here',
  })
  @IsString({ message: 'Firebase ID phải là chuỗi' })
  firebaseId: string;

  @ApiProperty({
    description: 'User role',
    example: 'USER',
    enum: ['USER', 'ADMIN'],
    required: false,
  })
  @IsOptional()
  @IsEnum(Role)
  role?: Role;

  @ApiProperty({
    description: 'User active status',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}

export class CreateUserWithSettingsDto {
  @ApiProperty({
    description: 'User email',
    example: '<EMAIL>',
  })
  @IsEmail({}, { message: 'Email không hợp lệ' })
  email: string;

  @ApiProperty({
    description: 'Firebase ID của user',
    example: 'firebase_uid_here',
  })
  @IsString({ message: 'Firebase ID phải là chuỗi' })
  firebaseId: string;

  @ApiProperty({
    description: 'Role của user',
    enum: Role,
    default: Role.USER,
  })
  @IsEnum(Role)
  role: Role = Role.USER;

  @ApiProperty({
    description: 'Trạng thái active của user',
    default: true,
  })
  @IsBoolean()
  is_active: boolean = true;

  @ApiProperty({
    description: 'Custom settings cho user',
    required: false,
    example: {
      theme: 'dark',
      language: 'en',
      notifications_enabled: 'true',
    },
  })
  @IsOptional()
  customSettings?: Record<string, string>;
}

export class UpdateUserDto {
  @ApiProperty({
    description: 'User email',
    example: '<EMAIL>',
    required: false,
  })
  @IsOptional()
  @IsEmail({}, { message: 'Email không hợp lệ' })
  email?: string;

  @ApiProperty({
    description: 'User role',
    example: 'USER',
    enum: ['USER', 'ADMIN'],
    required: false,
  })
  @IsOptional()
  @IsEnum(Role)
  role?: string;

  @ApiProperty({
    description: 'User active status',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @ApiProperty({ description: 'MFA secret', required: false })
  @IsString()
  @IsOptional()
  mfaSecret?: string;

  @ApiProperty({ description: 'Use MFA secret', required: false })
  @IsBoolean()
  @IsOptional()
  useMfaSecret?: boolean;
}
