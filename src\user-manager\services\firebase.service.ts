import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as admin from 'firebase-admin';
import axios from 'axios';

@Injectable()
export class FirebaseService {
  private readonly logger = new Logger(FirebaseService.name);

  constructor(private configService: ConfigService) {
    // Initialize Firebase Admin SDK if not already initialized
    if (!admin.apps.length) {
      const projectId = this.configService.get<string>('FIREBASE_PROJECT_ID');
      const privateKey = this.configService
        .get<string>('FIREBASE_PRIVATE_KEY')
        ?.replace(/\\n/g, '\n');
      const clientEmail = this.configService.get<string>(
        'FIREBASE_CLIENT_EMAIL',
      );

      if (!projectId || !privateKey || !clientEmail) {
        this.logger.error(
          'Firebase configuration is missing. Please check environment variables.',
        );
        throw new Error(
          'Firebase configuration is incomplete. Please set FIREBASE_PROJECT_ID, FIREBASE_PRIVATE_KEY, and FIREBASE_CLIENT_EMAIL.',
        );
      }

      try {
        admin.initializeApp({
          credential: admin.credential.cert({
            projectId,
            privateKey,
            clientEmail,
          }),
          projectId,
          databaseURL: this.configService.get<string>('FIREBASE_DATABASE_URL'),
          storageBucket: this.configService.get<string>(
            'FIREBASE_STORAGE_BUCKET',
          ),
        });

        this.logger.log(
          `Firebase Admin SDK initialized for project: ${projectId}`,
        );
      } catch (error) {
        this.logger.error('Error initializing Firebase Admin SDK:', error);
        throw error;
      }
    }
  }

  /**
   * Verify Firebase ID token with better error handling
   */
  async verifyIdToken(idToken: string): Promise<admin.auth.DecodedIdToken> {
    try {
      const decodedToken = await admin.auth().verifyIdToken(idToken);
      return decodedToken;
    } catch (error) {
      this.logger.error('Token verification failed with error:', error.message);

      // Check if token is expired
      if (
        error.code === 'auth/id-token-expired' ||
        error.message.includes('expired')
      ) {
        this.logger.warn(
          'Firebase token is expired. Please refresh the token.',
        );
        throw new Error(
          'Firebase token is expired. Please refresh the token and try again.',
        );
      }

      // Check if token is invalid
      if (
        error.code === 'auth/invalid-id-token' ||
        error.message.includes('invalid')
      ) {
        this.logger.warn('Firebase token is invalid.');
        throw new Error('Invalid Firebase token. Please get a new token.');
      }

      throw new Error(`Firebase token verification failed: ${error.message}`);
    }
  }

  /**
   * Get user by Firebase UID
   */
  async getUserByUid(uid: string): Promise<admin.auth.UserRecord> {
    try {
      const userRecord = await admin.auth().getUser(uid);
      return userRecord;
    } catch (error) {
      this.logger.error('Error getting user by UID:', error);
      throw new Error('User not found in Firebase');
    }
  }

  /**
   * Create user in Firebase Auth
   */
  async createUser(userData: {
    email: string;
    password?: string;
    displayName?: string;
    phoneNumber?: string;
    photoURL?: string;
  }): Promise<admin.auth.UserRecord> {
    try {
      const userRecord = await admin.auth().createUser({
        email: userData.email,
        password: userData.password,
        displayName: userData.displayName,
        phoneNumber: userData.phoneNumber,
        photoURL: userData.photoURL,
        emailVerified: false,
      });

      this.logger.log(`Successfully created Firebase user: ${userRecord.uid}`);
      return userRecord;
    } catch (error) {
      this.logger.error('Error creating Firebase user:', error);
      throw new Error(`Failed to create Firebase user: ${error.message}`);
    }
  }

  /**
   * Update user in Firebase Auth
   */
  async updateUser(
    uid: string,
    userData: {
      displayName?: string;
      phoneNumber?: string;
      photoURL?: string;
      email?: string;
    },
  ): Promise<admin.auth.UserRecord> {
    try {
      const userRecord = await admin.auth().updateUser(uid, userData);
      this.logger.log(`Successfully updated Firebase user: ${uid}`);
      return userRecord;
    } catch (error) {
      this.logger.error('Error updating Firebase user:', error);
      throw new Error(`Failed to update Firebase user: ${error.message}`);
    }
  }

  /**
   * Delete user from Firebase Auth
   */
  async deleteUser(uid: string): Promise<void> {
    try {
      await admin.auth().deleteUser(uid);
      this.logger.log(`Successfully deleted Firebase user: ${uid}`);
    } catch (error) {
      this.logger.error('Error deleting Firebase user:', error);
      throw new Error(`Failed to delete Firebase user: ${error.message}`);
    }
  }

  /**
   * Set custom claims for user
   */
  async setCustomClaims(uid: string, claims: object): Promise<void> {
    try {
      await admin.auth().setCustomUserClaims(uid, claims);
      this.logger.log(`Successfully set custom claims for user: ${uid}`);
    } catch (error) {
      this.logger.error('Error setting custom claims:', error);
      throw new Error(`Failed to set custom claims: ${error.message}`);
    }
  }

  /**
   * Xác thực mật khẩu qua Firebase REST API
   */
  async verifyPasswordWithFirebase(
    email: string,
    password: string,
  ): Promise<boolean> {
    const apiKey = this.configService.get<string>('FIREBASE_API_KEY');
    if (!apiKey) throw new Error('Missing FIREBASE_API_KEY');
    try {
      const res = await axios.post(
        `https://identitytoolkit.googleapis.com/v1/accounts:signInWithPassword?key=${apiKey}`,
        {
          email,
          password,
          returnSecureToken: false,
        },
        { headers: { 'Content-Type': 'application/json' } },
      );
      return !!res.data && !!res.data.localId;
    } catch (e) {
      return false;
    }
  }

  /**
   * List all users in Firebase Auth
   */
  async listAllUsers(): Promise<admin.auth.UserRecord[]> {
    const users: admin.auth.UserRecord[] = [];
    let nextPageToken: string | undefined = undefined;
    do {
      const result = await admin.auth().listUsers(1000, nextPageToken);
      users.push(...result.users);
      nextPageToken = result.pageToken;
    } while (nextPageToken);
    return users;
  }
}
