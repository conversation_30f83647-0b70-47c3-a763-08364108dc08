import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsString, IsNotEmpty, IsOptional } from 'class-validator';

export class LoginDto {
  @ApiProperty({
    description: 'User email',
    example: '<EMAIL>',
  })
  @IsEmail({}, { message: 'Please provide a valid email' })
  @IsNotEmpty({ message: 'Email is required' })
  email: string;

  @ApiProperty({
    description: 'User password',
    example: 'Admin@123456',
  })
  @IsString({ message: 'Password must be a string' })
  @IsNotEmpty({ message: 'Password is required' })
  password: string;
}

export class RefreshTokenDto {
  @ApiProperty({
    description: 'Refresh token',
    example: 'refresh_token_here',
  })
  @IsString({ message: 'Refresh token must be a string' })
  @IsNotEmpty({ message: 'Refresh token is required' })
  refreshToken: string;
}

export class GenerateTokenByUidDto {
  @ApiProperty({
    description: 'Firebase UID',
    example: 'firebase_uid_here',
  })
  @IsString({ message: 'UID must be a string' })
  @IsNotEmpty({ message: 'UID is required' })
  uid: string;
}

export class TokenResponseDto {
  @ApiProperty({
    description: 'Firebase ID token',
    example: 'eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  token: string;

  @ApiProperty({
    description: 'Token expiration time in seconds',
    example: 3600,
  })
  expiresIn: number;

  @ApiProperty({
    description: 'Refresh token (optional)',
    example: 'refresh_token_here',
    required: false,
  })
  @IsOptional()
  refreshToken?: string;
}

export class AdminLoginResponseDto {
  @ApiProperty({
    description: 'Success status',
    example: true,
  })
  success: boolean;

  @ApiProperty({
    description: 'Response message',
    example: 'Login successful',
  })
  message: string;

  @ApiProperty({
    description: 'Token data',
    type: TokenResponseDto,
  })
  data: TokenResponseDto;

  @ApiProperty({
    description: 'User information',
    example: {
      id: '123e4567-e89b-12d3-a456-************',
      email: '<EMAIL>',
      role: 'ADMIN',
      firebaseId: 'firebase_uid_here',
    },
  })
  user: {
    id: string;
    email: string;
    role: string;
    firebaseId: string;
  };
}
