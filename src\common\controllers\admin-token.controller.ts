import {
  Controller,
  Get,
  Post,
  Body,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { AdminTokenService } from '../services/admin-token.service';

export class GetAdminInfoDto {
  email: string;
}

export class VerifyAdminTokenDto {
  token: string;
}

@ApiTags('Admin Token')
@Controller('admin-token')
export class AdminTokenController {
  constructor(private readonly adminTokenService: AdminTokenService) {}

  @Get('instructions')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get instructions for getting Firebase ID token',
    description: 'Hướng dẫn cách lấy Firebase ID token cho admin',
  })
  @ApiResponse({
    status: 200,
    description: 'Instructions retrieved successfully',
  })
  getTokenInstructions() {
    return this.adminTokenService.getTokenInstructions();
  }

  @Get('default-admin')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get default admin info',
    description: '<PERSON><PERSON>y thông tin admin mặc định (<EMAIL>)',
  })
  @ApiResponse({
    status: 200,
    description: 'Default admin info retrieved successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'Default admin not found',
  })
  getDefaultAdminInfo() {
    return this.adminTokenService.getDefaultAdminInfo();
  }

  @Post('admin-info')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get admin info by email',
    description: 'Lấy thông tin admin theo email',
  })
  @ApiResponse({
    status: 200,
    description: 'Admin info retrieved successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'Admin not found',
  })
  getAdminInfo(@Body() getAdminInfoDto: GetAdminInfoDto) {
    return this.adminTokenService.getAdminInfo(getAdminInfoDto.email);
  }

  @Post('verify')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Verify admin token',
    description: 'Verify Firebase ID token của admin',
  })
  @ApiResponse({
    status: 200,
    description: 'Admin token verified successfully',
  })
  @ApiResponse({
    status: 401,
    description: 'Invalid token or user is not admin',
  })
  verifyAdminToken(@Body() verifyAdminTokenDto: VerifyAdminTokenDto) {
    return this.adminTokenService.verifyAdminToken(verifyAdminTokenDto.token);
  }
}
