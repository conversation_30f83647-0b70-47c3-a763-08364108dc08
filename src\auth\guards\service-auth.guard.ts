import {
  Injectable,
  CanActivate,
  ExecutionContext,
  UnauthorizedException,
  Logger,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Request as ExpressRequest } from 'express';

/**
 * Guard for service-to-service authentication using API keys
 */
@Injectable()
export class ServiceAuthGuard implements CanActivate {
  private readonly logger = new Logger(ServiceAuthGuard.name);
  private readonly validApiKeys: Set<string>;

  constructor(private readonly configService: ConfigService) {
    // Load valid API keys from environment variables
    const apiKeys = this.configService.get<string>('SERVICE_API_KEYS', '');
    this.validApiKeys = new Set(
      apiKeys.split(',').map(key => key.trim()).filter(key => key.length > 0)
    );

    // Add default Chat Service API key if configured
    const chatServiceKey = this.configService.get<string>('CHAT_SERVICE_API_KEY');
    if (chatServiceKey) {
      this.validApiKeys.add(chatServiceKey);
    }

    this.logger.log(`Loaded ${this.validApiKeys.size} service API keys`);
  }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request: ExpressRequest = context.switchToHttp().getRequest();

    try {
      // Check for API key in headers
      const apiKey = this.extractApiKey(request);
      
      if (!apiKey) {
        this.logger.warn('No API key provided in request');
        throw new UnauthorizedException('Service API key required');
      }

      // Validate API key
      if (!this.validApiKeys.has(apiKey)) {
        this.logger.warn(`Invalid API key attempted: ${apiKey.substring(0, 8)}...`);
        throw new UnauthorizedException('Invalid service API key');
      }

      // Add service info to request for logging/tracking
      (request as any).serviceAuth = {
        type: 'service',
        apiKey: apiKey.substring(0, 8) + '...',
        timestamp: new Date().toISOString(),
      };

      this.logger.debug(`Service authenticated with API key: ${apiKey.substring(0, 8)}...`);
      return true;

    } catch (error) {
      if (error instanceof UnauthorizedException) {
        throw error;
      }
      this.logger.error('Service authentication error:', error);
      throw new UnauthorizedException('Service authentication failed');
    }
  }

  private extractApiKey(request: ExpressRequest): string | null {
    // Check X-API-Key header (preferred)
    const apiKeyHeader = request.headers['x-api-key'] as string;
    if (apiKeyHeader) {
      return apiKeyHeader;
    }

    // Check Authorization header with "Service" scheme
    const authHeader = request.headers.authorization;
    if (authHeader && authHeader.startsWith('Service ')) {
      return authHeader.replace('Service ', '');
    }

    // Check query parameter (less secure, for development only)
    const apiKeyQuery = request.query.apiKey as string;
    if (apiKeyQuery && process.env.NODE_ENV === 'development') {
      this.logger.warn('API key provided via query parameter - not recommended for production');
      return apiKeyQuery;
    }

    return null;
  }
}

/**
 * Combined guard that allows both Firebase auth and Service auth
 */
@Injectable()
export class FlexibleAuthGuard implements CanActivate {
  private readonly logger = new Logger(FlexibleAuthGuard.name);

  constructor(
    private readonly serviceAuthGuard: ServiceAuthGuard,
    private readonly configService: ConfigService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request: ExpressRequest = context.switchToHttp().getRequest();

    try {
      // First try service authentication
      const hasApiKey = this.hasServiceApiKey(request);
      
      if (hasApiKey) {
        this.logger.debug('Attempting service authentication');
        return await this.serviceAuthGuard.canActivate(context);
      }

      // If no API key, require Firebase authentication
      const hasFirebaseToken = this.hasFirebaseToken(request);
      if (!hasFirebaseToken) {
        throw new UnauthorizedException('Either service API key or Firebase token required');
      }

      // For Firebase auth, we'll let the FirebaseAuthGuard handle it
      // This guard is meant to be used in combination with FirebaseAuthGuard
      this.logger.debug('Service API key not found, expecting Firebase authentication');
      return true;

    } catch (error) {
      if (error instanceof UnauthorizedException) {
        throw error;
      }
      this.logger.error('Flexible authentication error:', error);
      throw new UnauthorizedException('Authentication failed');
    }
  }

  private hasServiceApiKey(request: ExpressRequest): boolean {
    return !!(
      request.headers['x-api-key'] ||
      (request.headers.authorization && request.headers.authorization.startsWith('Service ')) ||
      (process.env.NODE_ENV === 'development' && request.query.apiKey)
    );
  }

  private hasFirebaseToken(request: ExpressRequest): boolean {
    const authHeader = request.headers.authorization;
    return !!(authHeader && authHeader.startsWith('Bearer ') && !authHeader.startsWith('Service '));
  }
}
