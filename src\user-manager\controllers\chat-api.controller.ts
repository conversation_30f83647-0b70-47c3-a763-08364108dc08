import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Query,
  HttpCode,
  HttpStatus,
  ValidationPipe,
  UsePipes,
  UseGuards,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { ChatApiService } from '../services/chat-api.service';
import {
  ChatUserInfoDto,
  ChatUserProfileDto,
  BatchUserRequestDto,
  UserSearchRequestDto,
} from '../dto/chat-user-info.dto';
import { FirebaseAuthGuard } from '../../auth/guards/auth.guards';
import { ServiceAuthGuard, FlexibleAuthGuard } from '../../auth/guards/service-auth.guard';

@ApiTags('Chat Service API')
@Controller('api/users')
@UsePipes(new ValidationPipe({ transform: true }))
@UseGuards(ServiceAuthGuard)
@ApiBearerAuth('JWT-auth')
export class ChatApiController {
  constructor(private readonly chatApiService: ChatApiService) {}

  @Get(':userId')
  @ApiOperation({ summary: 'Get user information by ID for Chat Service' })
  @ApiParam({ name: 'userId', description: 'User ID (UUID)' })
  @ApiResponse({
    status: 200,
    description: 'User information retrieved successfully',
    type: ChatUserInfoDto,
  })
  @ApiResponse({ status: 404, description: 'User not found' })
  async getUserById(@Param('userId') userId: string): Promise<ChatUserInfoDto> {
    const user = await this.chatApiService.getUserById(userId);
    if (!user) {
      throw new NotFoundException(`User with ID ${userId} not found`);
    }
    return user;
  }

  @Post('batch')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Get multiple users by their IDs' })
  @ApiResponse({
    status: 200,
    description: 'Users retrieved successfully',
    type: [ChatUserInfoDto],
  })
  async getUsersByIds(@Body() request: BatchUserRequestDto): Promise<ChatUserInfoDto[]> {
    if (!request.userIds || request.userIds.length === 0) {
      throw new BadRequestException('User IDs array cannot be empty');
    }
    return this.chatApiService.getUsersByIds(request.userIds);
  }

  @Get()
  @ApiOperation({ summary: 'Search users by username or display name' })
  @ApiQuery({ name: 'q', description: 'Search query' })
  @ApiQuery({ name: 'limit', description: 'Maximum number of results', required: false })
  @ApiResponse({
    status: 200,
    description: 'Users found successfully',
    type: [ChatUserInfoDto],
  })
  async searchUsers(
    @Query('q') query: string,
    @Query('limit') limit?: number,
  ): Promise<ChatUserInfoDto[]> {
    if (!query || query.trim().length === 0) {
      throw new BadRequestException('Search query cannot be empty');
    }
    const searchLimit = limit && limit > 0 ? Math.min(limit, 100) : 10;
    return this.chatApiService.searchUsers(query.trim(), searchLimit);
  }

  @Get(':userId/exists')
  @ApiOperation({ summary: 'Check if user exists' })
  @ApiParam({ name: 'userId', description: 'User ID (UUID)' })
  @ApiResponse({
    status: 200,
    description: 'User existence check completed',
    schema: { type: 'boolean' },
  })
  async userExists(@Param('userId') userId: string): Promise<boolean> {
    return this.chatApiService.userExists(userId);
  }

  @Post('validate')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Validate multiple user IDs and return valid ones' })
  @ApiResponse({
    status: 200,
    description: 'User IDs validated successfully',
    schema: { type: 'array', items: { type: 'string' } },
  })
  async validateUserIds(@Body() request: BatchUserRequestDto): Promise<string[]> {
    if (!request.userIds || request.userIds.length === 0) {
      return [];
    }
    return this.chatApiService.validateUserIds(request.userIds);
  }

  @Get(':userId/role')
  @ApiOperation({ summary: 'Get user role' })
  @ApiParam({ name: 'userId', description: 'User ID (UUID)' })
  @ApiResponse({
    status: 200,
    description: 'User role retrieved successfully',
    schema: { type: 'string' },
  })
  @ApiResponse({ status: 404, description: 'User not found' })
  async getUserRole(@Param('userId') userId: string): Promise<string> {
    const role = await this.chatApiService.getUserRole(userId);
    if (!role) {
      throw new NotFoundException(`User with ID ${userId} not found`);
    }
    return role;
  }

  @Get(':userId/profile')
  @ApiOperation({ summary: 'Get user profile with role' })
  @ApiParam({ name: 'userId', description: 'User ID (UUID)' })
  @ApiResponse({
    status: 200,
    description: 'User profile retrieved successfully',
    type: ChatUserProfileDto,
  })
  @ApiResponse({ status: 404, description: 'User not found' })
  async getUserProfile(@Param('userId') userId: string): Promise<ChatUserProfileDto> {
    const profile = await this.chatApiService.getUserProfile(userId);
    if (!profile) {
      throw new NotFoundException(`User profile with ID ${userId} not found`);
    }
    return profile;
  }
}
