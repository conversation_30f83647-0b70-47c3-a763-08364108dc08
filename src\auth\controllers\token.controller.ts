import {
  Controller,
  Post,
  Body,
  HttpCode,
  HttpStatus,
  UseGuards,
  Req,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { TokenService } from '../services/token.service';
import { MapperService } from '../../common/services/mapper.service';
import { AdminSeeder } from '../../common/seeders/admin.seeder';
import {
  LoginDto,
  RefreshTokenDto,
  GenerateTokenByUidDto,
  TokenResponseDto,
  AdminLoginResponseDto,
} from '../dto/token.dto';
import { FirebaseAuthGuard, RolesGuard } from '../guards/auth.guards';
import { Roles } from '../decorators/auth.decorators';
import { Role } from '../../common/enum/role.enum';
import { Request as ExpressRequest, Response } from 'express';

@ApiTags('Token Management')
@Controller('token')
export class TokenController {
  constructor(
    private readonly tokenService: TokenService,
    private readonly mapperService: MapperService,
    private readonly adminSeeder: AdminSeeder,
  ) {}

  @Post('admin/login')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Admin login and get token',
    description: 'Đăng nhập admin và lấy Firebase token',
  })
  @ApiResponse({
    status: 200,
    description: 'Login successful',
    type: AdminLoginResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - invalid credentials',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - invalid credentials',
  })
  async adminLogin(
    @Body() loginDto: LoginDto,
    @Req() req: ExpressRequest,
  ): Promise<AdminLoginResponseDto> {
    try {
      const { ip, device } = (req as any).context || {};
      const tokenData = await this.tokenService.generateTokenByCredentials(
        loginDto,
        ip,
        device,
      );
      // Get admin user info
      const adminUser = await this.adminSeeder.getAdminByEmail(loginDto.email);

      if (!adminUser) {
        throw new Error('Admin user not found');
      }

      return this.mapperService.createAdminLoginResponse(tokenData, adminUser);
    } catch (error) {
      throw error;
    }
  }

  @Post('refresh')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Refresh Firebase token',
    description: 'Làm mới Firebase token',
  })
  @ApiResponse({
    status: 200,
    description: 'Token refreshed successfully',
    type: TokenResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - invalid refresh token',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - invalid refresh token',
  })
  async refreshToken(
    @Body() refreshTokenDto: RefreshTokenDto,
  ): Promise<TokenResponseDto> {
    return this.tokenService.refreshToken(refreshTokenDto.refreshToken);
  }

  @Post('generate-by-uid')
  @HttpCode(HttpStatus.OK)
  @UseGuards(FirebaseAuthGuard, RolesGuard)
  @Roles(Role.ADMIN)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({
    summary: 'Generate token by Firebase UID (Admin only)',
    description: 'Tạo token cho user theo Firebase UID (Chỉ Admin)',
  })
  @ApiResponse({
    status: 200,
    description: 'Token generated successfully',
    type: TokenResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - requires ADMIN role',
  })
  async generateTokenByUid(
    @Body() generateTokenDto: GenerateTokenByUidDto,
  ): Promise<TokenResponseDto> {
    return this.tokenService.generateTokenByUid(generateTokenDto.uid);
  }

  @Post('verify')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Verify Firebase token',
    description: 'Xác thực Firebase token',
  })
  @ApiResponse({
    status: 200,
    description: 'Token verified successfully',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - invalid token',
  })
  async verifyToken(@Body() body: { token: string }) {
    try {
      const decodedToken = await this.tokenService.verifyToken(body.token);
      return {
        success: true,
        message: 'Token verified successfully',
        data: {
          uid: decodedToken.uid,
          email: decodedToken.email,
          role: decodedToken.role,
          userId: decodedToken.userId,
        },
      };
    } catch (error) {
      throw error;
    }
  }
}
