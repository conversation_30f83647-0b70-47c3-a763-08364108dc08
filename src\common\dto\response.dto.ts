import { ApiProperty } from '@nestjs/swagger';

// Base Response DTO
export class BaseResponseDto<T = any> {
  @ApiProperty({
    description: 'Success status',
    example: true,
  })
  success: boolean;

  @ApiProperty({
    description: 'Response message',
    example: 'Operation completed successfully',
  })
  message: string;

  @ApiProperty({
    description: 'Response data',
    required: false,
  })
  data?: T;
}

// Error Response DTO
export class ErrorResponseDto {
  @ApiProperty({
    description: 'Success status',
    example: false,
  })
  success: boolean;

  @ApiProperty({
    description: 'Error message',
    example: 'Operation failed',
  })
  message: string;

  @ApiProperty({
    description: 'Error details',
    required: false,
  })
  error?: any;
}

// User Response DTOs
export class UserResponseDto {
  @ApiProperty({
    description: 'User ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  id: string;

  @ApiProperty({
    description: 'User email',
    example: '<EMAIL>',
  })
  email: string;

  @ApiProperty({
    description: 'Firebase ID',
    example: 'firebase_uid_here',
  })
  firebaseId: string;

  @ApiProperty({
    description: 'User role',
    example: 'USER',
  })
  role: string;

  @ApiProperty({
    description: 'User active status',
    example: true,
  })
  is_active: boolean;

  @ApiProperty({
    description: 'MFA enabled status',
    example: false,
  })
  useMfaSecret: boolean;
}

// Firebase User Response DTO
export class FirebaseUserResponseDto {
  @ApiProperty({
    description: 'Firebase UID',
    example: 'firebase_uid_here',
  })
  uid: string;

  @ApiProperty({
    description: 'Firebase email',
    example: '<EMAIL>',
  })
  email: string;

  @ApiProperty({
    description: 'Firebase display name',
    example: 'John Doe',
    required: false,
  })
  displayName?: string;
}

// Profile Response DTO
export class ProfileResponseDataDto {
  @ApiProperty({
    description: 'Profile ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  id: string;
  @ApiProperty({ description: 'Full name', example: 'John Doe' })
  full_name: string;
  @ApiProperty({
    description: 'Phone number',
    example: '+**********',
    required: false,
  })
  phone_number?: string;
  @ApiProperty({
    description: 'Avatar URL',
    example: 'https://example.com/avatar.jpg',
    required: false,
  })
  avatar_url?: string;
  @ApiProperty({
    description: 'Company name',
    example: 'Bright Company',
    required: false,
  })
  company?: string;
  @ApiProperty({
    description: 'Job title',
    example: 'Software Engineer',
    required: false,
  })
  occupation?: string;
  @ApiProperty({
    description: 'Bio',
    example: 'I am a software engineer...',
    required: false,
  })
  bio?: string;
  @ApiProperty({
    description: 'Address line 1',
    example: '123 Main St',
    required: false,
  })
  address1?: string;
  @ApiProperty({
    description: 'Address line 2',
    example: 'Apt 4B',
    required: false,
  })
  address2?: string;
  @ApiProperty({ description: 'City', example: 'New York', required: false })
  city?: string;
  @ApiProperty({
    description: 'State/Province',
    example: 'NY',
    required: false,
  })
  state?: string;
  @ApiProperty({
    description: 'ZIP/Postal code',
    example: '10001',
    required: false,
  })
  zipCode?: string;
  @ApiProperty({ description: 'Country', example: 'USA', required: false })
  country?: string;
  @ApiProperty({
    description: 'Gender',
    example: 'male',
    enum: ['male', 'female', 'other'],
    required: false,
  })
  gender?: string;
  @ApiProperty({
    description: 'Last login date',
    example: '2024-01-01T00:00:00.000Z',
    required: false,
  })
  lastLogin?: Date;
  @ApiProperty({
    description: 'Date of birth',
    example: '1990-01-01',
    required: false,
  })
  dateOfBirth?: Date;
  @ApiProperty({
    description: 'Avatar image URL',
    example: 'https://example.com/avatar.jpg',
    required: false,
  })
  avatarImage?: string;
  @ApiProperty({
    description: 'Cover image URL',
    example: 'https://example.com/cover.jpg',
    required: false,
  })
  coverImage?: string;
  @ApiProperty({
    description: 'Facebook profile URL',
    example: 'https://facebook.com/username',
    required: false,
  })
  facebookUrl?: string;
  @ApiProperty({
    description: 'Twitter profile URL',
    example: 'https://twitter.com/username',
    required: false,
  })
  twitterUrl?: string;
  @ApiProperty({
    description: 'LinkedIn profile URL',
    example: 'https://linkedin.com/in/username',
    required: false,
  })
  linkedinUrl?: string;
}

// Settings Response DTO
export class SettingsResponseDto {
  @ApiProperty({
    description: 'Setting key',
    example: 'theme',
  })
  key: string;

  @ApiProperty({
    description: 'Setting value',
    example: 'dark',
  })
  value: string;

  @ApiProperty({
    description: 'Description of the setting',
    example: 'Giao diện sáng/tối',
    required: false,
  })
  description?: string;

  @ApiProperty({
    description: 'Data type of the setting',
    example: 'string',
    required: false,
  })
  datatype?: string;
}

// Email Queue Response DTO
export class EmailQueueResponseDto {
  @ApiProperty({
    description: 'Email queue ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  id: string;

  @ApiProperty({
    description: 'Email status',
    example: 'PENDING',
  })
  status: string;

  @ApiProperty({
    description: 'Email type',
    example: 'WELCOME',
  })
  email_type: string;
}

// Quick Create User Response DTO
export class QuickCreateUserResponseDto extends BaseResponseDto<{
  user: UserResponseDto;
  profile: ProfileResponseDataDto;
  settings: SettingsResponseDto[];
}> {
  @ApiProperty({
    description: 'Email queue information',
    required: false,
  })
  emailQueue?: EmailQueueResponseDto;
}

// Bulk Create Response DTO
export class BulkCreateResponseDto extends BaseResponseDto<{
  total: number;
  successful: number;
  failed: number;
  results: any[];
  errors: any[];
}> {
  @ApiProperty({
    description: 'Total number of users to create',
    example: 10,
  })
  total: number;

  @ApiProperty({
    description: 'Number of successfully created users',
    example: 8,
  })
  successful: number;

  @ApiProperty({
    description: 'Number of failed creations',
    example: 2,
  })
  failed: number;

  @ApiProperty({
    description: 'Successful results',
    type: [Object],
  })
  results: any[];

  @ApiProperty({
    description: 'Error details',
    type: [Object],
  })
  errors: any[];
}

// Avatar Upload Response DTO
export class AvatarUploadResponseDto extends BaseResponseDto<{
  avatarUrl: string;
  filename?: string;
  size?: number;
  mimetype?: string;
}> {
  @ApiProperty({
    description: 'Avatar URL',
    example: 'https://example.com/avatar.jpg',
  })
  avatarUrl: string;

  @ApiProperty({
    description: 'File name',
    example: 'avatar.jpg',
    required: false,
  })
  filename?: string;

  @ApiProperty({
    description: 'File size in bytes',
    example: 1024000,
    required: false,
  })
  size?: number;

  @ApiProperty({
    description: 'File MIME type',
    example: 'image/jpeg',
    required: false,
  })
  mimetype?: string;
}

// Account Status Response DTO
export class AccountStatusResponseDto extends BaseResponseDto<{
  isActive: boolean;
}> {
  @ApiProperty({
    description: 'Account active status',
    example: true,
  })
  isActive: boolean;
}

// Admin Seeding Response DTO
export class AdminSeedingResponseDto extends BaseResponseDto<{
  user: UserResponseDto;
  profile: ProfileResponseDataDto;
  settings: SettingsResponseDto[];
  firebaseUser: FirebaseUserResponseDto;
}> {}

// Auth Response DTOs
export class AuthVerifyResponseDto extends BaseResponseDto<{
  user: UserResponseDto;
  profile: any;
  settings: any[];
  firebase: {
    uid: string;
    email: string;
    email_verified: boolean;
    name?: string;
    picture?: string;
  };
  token: string;
}> {}

export class AuthTestResponseDto extends BaseResponseDto<{
  user: any;
  timestamp: string;
}> {
  @ApiProperty({
    description: 'Current timestamp',
    example: '2024-01-01T00:00:00.000Z',
  })
  timestamp: string;
}

// Token Response DTOs
export class TokenResponseDto {
  @ApiProperty({
    description: 'Access token',
    example:
      '**************************************************************...',
  })
  accessToken: string;

  @ApiProperty({
    description: 'Token type',
    example: 'Bearer',
  })
  tokenType: string;

  @ApiProperty({
    description: 'Token expiration time in seconds',
    example: 3600,
  })
  expiresIn: number;
}

export class AdminLoginResponseDto {
  @ApiProperty({
    description: 'Success status',
    example: true,
  })
  success: boolean;

  @ApiProperty({
    description: 'Response message',
    example: 'Admin login successful',
  })
  message: string;

  @ApiProperty({
    description: 'Token data',
    type: TokenResponseDto,
  })
  data: TokenResponseDto;

  @ApiProperty({
    description: 'User information',
    type: UserResponseDto,
  })
  user: UserResponseDto;
}
