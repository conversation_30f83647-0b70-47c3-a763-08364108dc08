# Auth Module

Module xử lý authentication và authorization sử dụng Firebase token.

## Tính năng

- ✅ Verify Firebase ID token
- ✅ Lấy thông tin user profile và settings
- ✅ Role-based authorization (ADMIN, MODERATOR, USER)
- ✅ JWT token cho internal use
- ✅ Logout functionality
- ✅ User permissions management
- ✅ Protected endpoints với guards

## Cài đặt

Module đã được cấu hình sẵn trong `app.module.ts`. Cần đảm bảo các environment variables:

```env
JWT_SECRET=your-secret-key
JWT_EXPIRES_IN=24h
FIREBASE_PROJECT_ID=your-project-id
FIREBASE_PRIVATE_KEY=your-private-key
FIREBASE_CLIENT_EMAIL=your-client-email
```

## API Endpoints

### 1. Verify Firebase Token
```http
POST /auth/verify
Content-Type: application/json

{
  "token": "firebase_id_token_from_frontend"
}
```

**Response:**
```json
{
  "success": true,
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "firebaseId": "firebase_uid",
    "role": "USER",
    "is_active": true
  },
  "profile": {
    "full_name": "Nguyễn Văn A",
    "phone": "+84123456789",
    "avatar": "https://example.com/avatar.jpg"
  },
  "settings": [
    {
      "key": "theme",
      "value": "light"
    }
  ],
  "firebase": {
    "uid": "firebase_uid",
    "email": "<EMAIL>",
    "email_verified": true
  },
  "token": "jwt_token_for_internal_use",
  "message": "Token verified successfully"
}
```

### 2. Get User Profile
```http
GET /auth/profile
Authorization: Bearer firebase_id_token_from_frontend
```

### 3. Get User Permissions
```http
GET /auth/permissions
Authorization: Bearer firebase_id_token_from_frontend
```

**Response:**
```json
{
  "success": true,
  "role": "ADMIN",
  "permissions": {
    "can_read_users": true,
    "can_create_users": true,
    "can_update_users": true,
    "can_delete_users": true,
    "can_manage_settings": true,
    "can_view_reports": true,
    "can_manage_roles": true
  }
}
```

### 4. Logout
```http
POST /auth/logout
Authorization: Bearer firebase_id_token_from_frontend
```

### 5. Admin Only Endpoint
```http
GET /auth/admin-only
Authorization: Bearer firebase_id_token_from_frontend
```

### 6. Moderator or Admin Endpoint
```http
GET /auth/moderator-or-admin
Authorization: Bearer firebase_id_token_from_frontend
```

### 7. Public Endpoint (No Auth Required)
```http
GET /auth/public
```

### 8. Protected Endpoint (Auth Required)
```http
GET /auth/protected
Authorization: Bearer firebase_id_token_from_frontend
```

## Guards

### FirebaseAuthGuard
Bảo vệ endpoints yêu cầu authentication:

```typescript
@UseGuards(FirebaseAuthGuard)
@Get('protected')
getProtectedData() {
  return 'This is protected data';
}
```

### RolesGuard
Bảo vệ endpoints theo role:

```typescript
@UseGuards(FirebaseAuthGuard, RolesGuard)
@Roles(Role.ADMIN)
@Get('admin-only')
adminOnly() {
  return 'Admin only data';
}
```

## Decorators

### @CurrentUser()
Lấy thông tin user từ request:

```typescript
@Get('profile')
@UseGuards(FirebaseAuthGuard)
getProfile(@CurrentUser('id') userId: number) {
  return this.authService.getCurrentUser(userId);
}
```

### @Roles()
Chỉ định role cần thiết:

```typescript
@Roles(Role.ADMIN, Role.MODERATOR)
@Get('moderator-or-admin')
moderatorOrAdmin() {
  return 'Moderator or Admin data';
}
```

## User Roles

- **ADMIN**: Full access to all features
- **MODERATOR**: Can view reports and manage some content
- **USER**: Basic access to user features

## Permissions

Mỗi role có các permissions khác nhau:

| Permission | ADMIN | MODERATOR | USER |
|------------|-------|-----------|------|
| can_read_users | ✅ | ✅ | ✅ |
| can_create_users | ✅ | ❌ | ❌ |
| can_update_users | ✅ | ❌ | ❌ |
| can_delete_users | ✅ | ❌ | ❌ |
| can_manage_settings | ✅ | ❌ | ❌ |
| can_view_reports | ✅ | ✅ | ❌ |
| can_manage_roles | ✅ | ❌ | ❌ |

## Error Handling

### 401 Unauthorized
```json
{
  "statusCode": 401,
  "message": "Invalid token",
  "error": "Unauthorized"
}
```

### 403 Forbidden
```json
{
  "statusCode": 403,
  "message": "Forbidden - requires ADMIN role",
  "error": "Forbidden"
}
```

## Usage trong Frontend

### 1. Login với Firebase
```javascript
// Frontend login với Firebase
const userCredential = await signInWithEmailAndPassword(auth, email, password);
const idToken = await userCredential.user.getIdToken();

// Gửi token lên backend để verify
const response = await fetch('/auth/verify', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({ token: idToken })
});

const data = await response.json();
// Lưu user info và token vào state/store
```

### 2. Gọi API với Authentication
```javascript
// Sử dụng token để gọi các protected endpoints
const response = await fetch('/auth/profile', {
  headers: {
    'Authorization': `Bearer ${idToken}`,
    'Content-Type': 'application/json',
  }
});
```

### 3. Check Permissions
```javascript
const permissionsResponse = await fetch('/auth/permissions', {
  headers: {
    'Authorization': `Bearer ${idToken}`,
  }
});

const { permissions } = await permissionsResponse.json();

if (permissions.can_create_users) {
  // Hiển thị nút tạo user
}
```

## Testing

Sử dụng file `auth-example.ts` để test các endpoints:

```bash
# Test verify token
curl -X POST http://localhost:3000/auth/verify \
  -H "Content-Type: application/json" \
  -d '{"token": "your_firebase_token"}'

# Test protected endpoint
curl -X GET http://localhost:3000/auth/profile \
  -H "Authorization: Bearer your_firebase_token"
```

## Security Notes

1. **Token Expiration**: Firebase tokens có thời hạn, cần refresh khi cần thiết
2. **HTTPS Only**: Luôn sử dụng HTTPS trong production
3. **Token Storage**: Lưu token an toàn (HttpOnly cookies hoặc secure storage)
4. **Role Validation**: Luôn validate role ở cả frontend và backend
5. **Logout**: Implement proper logout để clear tokens 