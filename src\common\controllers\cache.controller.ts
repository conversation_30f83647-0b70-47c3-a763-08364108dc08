import {
  Controller,
  Get,
  Delete,
  UseGuards,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { RedisService } from '../services/redis.service';
import { FirebaseAuthGuard, RolesGuard } from '../../auth/guards/auth.guards';
import { Roles } from '../../auth/decorators/auth.decorators';
import { Role } from '../enum/role.enum';

@ApiTags('Cache Management')
@Controller('cache')
@UseGuards(FirebaseAuthGuard, RolesGuard)
@ApiBearerAuth('JWT-auth')
export class CacheController {
  constructor(private readonly redisService: RedisService) {}

  @Get('stats')
  @ApiOperation({
    summary: 'Get cache statistics',
    description: '<PERSON><PERSON><PERSON> thống kê về Redis cache (Admin only)',
  })
  @ApiResponse({
    status: 200,
    description: 'Cache statistics retrieved successfully',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - requires ADMIN role',
  })
  @Roles(Role.ADMIN)
  async getCacheStats() {
    const stats = await this.redisService.getStats();
    return {
      success: true,
      message: 'Cache statistics retrieved successfully',
      data: stats,
    };
  }

  @Get('test')
  @ApiOperation({
    summary: 'Test Redis cache',
    description: 'Test Redis cache connection và operations (Admin only)',
  })
  @ApiResponse({
    status: 200,
    description: 'Redis test completed',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - requires ADMIN role',
  })
  @Roles(Role.ADMIN)
  async testCache() {
    try {
      // Test set
      const testKey = 'test:connection';
      const testValue = {
        message: 'Redis is working!',
        timestamp: new Date().toISOString(),
      };

      console.log('Starting Redis test...');
      await this.redisService.set(testKey, testValue, { ttl: 60 }); // 1 minute

      // Test get
      const retrievedValue = await this.redisService.get(testKey);

      // Test delete
      await this.redisService.del(testKey);

      return {
        success: true,
        message: 'Redis cache test completed successfully',
        data: {
          set: 'OK',
          get: retrievedValue ? 'OK' : 'FAILED',
          delete: 'OK',
          retrievedValue,
          environment: {
            REDIS_HOST: process.env.REDIS_HOST || 'localhost',
            REDIS_PORT: process.env.REDIS_PORT || 6379,
            REDIS_PASSWORD: process.env.REDIS_PASSWORD ? 'SET' : 'NOT_SET',
            REDIS_DB: process.env.REDIS_DB || 0,
            NODE_ENV: process.env.NODE_ENV || 'development',
          },
        },
      };
    } catch (error) {
      return {
        success: false,
        message: 'Redis cache test failed',
        error: error.message,
        stack: error.stack,
        environment: {
          REDIS_HOST: process.env.REDIS_HOST || 'localhost',
          REDIS_PORT: process.env.REDIS_PORT || 6379,
          REDIS_PASSWORD: process.env.REDIS_PASSWORD ? 'SET' : 'NOT_SET',
          REDIS_DB: process.env.REDIS_DB || 0,
          NODE_ENV: process.env.NODE_ENV || 'development',
        },
      };
    }
  }

  @Get('env')
  @ApiOperation({
    summary: 'Check environment variables',
    description: 'Kiểm tra environment variables cho Redis (Admin only)',
  })
  @ApiResponse({
    status: 200,
    description: 'Environment variables checked',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - requires ADMIN role',
  })
  @Roles(Role.ADMIN)
  async checkEnvironment() {
    return {
      success: true,
      message: 'Environment variables checked',
      data: {
        redis: {
          REDIS_HOST: process.env.REDIS_HOST || 'localhost (default)',
          REDIS_PORT: process.env.REDIS_PORT || '6379 (default)',
          REDIS_PASSWORD: process.env.REDIS_PASSWORD ? 'SET' : 'NOT_SET',
          REDIS_DB: process.env.REDIS_DB || '0 (default)',
          REDIS_TTL: process.env.REDIS_TTL || '3600 (default)',
          REDIS_MAX_ITEMS: process.env.REDIS_MAX_ITEMS || '100 (default)',
        },
        app: {
          NODE_ENV: process.env.NODE_ENV || 'development (default)',
          PORT: process.env.PORT || '3000 (default)',
        },
        database: {
          DB_HOST: process.env.DB_HOST || 'localhost (default)',
          DB_PORT: process.env.DB_PORT || '5432 (default)',
          DB_NAME: process.env.DB_NAME || 'bright_db (default)',
        },
      },
    };
  }

  @Delete('reset')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Reset all cache',
    description: 'Xóa tất cả cache trong Redis (Admin only)',
  })
  @ApiResponse({
    status: 200,
    description: 'Cache reset successfully',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - requires ADMIN role',
  })
  @Roles(Role.ADMIN)
  async resetCache() {
    await this.redisService.reset();
    return {
      success: true,
      message: 'Cache reset successfully',
    };
  }
}
