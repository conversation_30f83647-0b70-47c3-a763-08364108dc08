import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsOptional, IsString } from 'class-validator';

export class DeactivateAccountDto {
  @ApiProperty({
    description: 'Confirmation to deactivate account',
    example: true,
  })
  @IsBoolean({ message: 'confirm must be a boolean' })
  confirm: boolean;

  @ApiProperty({
    description: 'Reason for deactivation',
    example: 'User requested account deactivation',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Reason must be a string' })
  reason?: string;
}
