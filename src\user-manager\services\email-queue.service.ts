import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import {
  EmailQueue,
  EmailType,
  EmailStatus,
} from '../entities/email-queue.entity';

export interface CreateEmailQueueDto {
  to_email: string;
  subject: string;
  content: string;
  email_type: EmailType;
  metadata?: Record<string, any>;
  scheduled_at?: Date;
}

@Injectable()
export class EmailQueueService {
  private readonly logger = new Logger(EmailQueueService.name);

  constructor(
    @InjectRepository(EmailQueue)
    private emailQueueRepository: Repository<EmailQueue>,
  ) {}

  /**
   * Tạo email queue entry
   */
  async createEmailQueue(
    createEmailQueueDto: CreateEmailQueueDto,
  ): Promise<EmailQueue> {
    try {
      const emailQueue = this.emailQueueRepository.create({
        ...createEmailQueueDto,
        status: EmailStatus.PENDING,
        retry_count: 0,
      });

      const savedEmailQueue = await this.emailQueueRepository.save(emailQueue);
      this.logger.log(
        `Created email queue entry for ${createEmailQueueDto.to_email}`,
      );

      return savedEmailQueue;
    } catch (error) {
      this.logger.error('Error creating email queue entry:', error);
      throw new Error('Failed to create email queue entry');
    }
  }

  /**
   * Tạo welcome email với password
   */
  async createWelcomeEmail(
    email: string,
    fullName: string,
    password: string,
    loginUrl: string = 'https://your-app.com/login',
  ): Promise<EmailQueue> {
    const subject = 'Chào mừng bạn đến với Bright User Management System!';
    const content = this.generateWelcomeEmailContent(
      email,
      fullName,
      password,
      loginUrl,
    );

    return await this.createEmailQueue({
      to_email: email,
      subject,
      content,
      email_type: EmailType.WELCOME,
      metadata: {
        fullName,
        password,
        loginUrl,
      },
    });
  }

  /**
   * Tạo password reset email
   */
  async createPasswordResetEmail(
    email: string,
    resetToken: string,
    resetUrl: string,
  ): Promise<EmailQueue> {
    const subject = 'Đặt lại mật khẩu - Bright User Management System';
    const content = this.generatePasswordResetEmailContent(
      resetToken,
      resetUrl,
    );

    return await this.createEmailQueue({
      to_email: email,
      subject,
      content,
      email_type: EmailType.PASSWORD_RESET,
      metadata: {
        resetToken,
        resetUrl,
      },
    });
  }

  /**
   * Lấy tất cả pending emails
   */
  async getPendingEmails(): Promise<EmailQueue[]> {
    try {
      return await this.emailQueueRepository.find({
        where: { status: EmailStatus.PENDING },
        order: { created_at: 'ASC' },
      });
    } catch (error) {
      this.logger.error('Error getting pending emails:', error);
      throw new Error('Failed to get pending emails');
    }
  }

  /**
   * Cập nhật trạng thái email
   */
  async updateEmailStatus(
    emailId: number,
    status: EmailStatus,
    errorMessage?: string,
  ): Promise<EmailQueue> {
    try {
      const emailQueue = await this.emailQueueRepository.findOne({
        where: { id: emailId } as any,
      });

      if (!emailQueue) {
        throw new Error('Email queue entry not found');
      }

      emailQueue.status = status;
      if (status === EmailStatus.SENT) {
        emailQueue.sent_at = new Date();
      } else if (status === EmailStatus.FAILED) {
        emailQueue.error_message = errorMessage;
        emailQueue.retry_count = (emailQueue.retry_count || 0) + 1;
      }

      const updatedEmailQueue =
        await this.emailQueueRepository.save(emailQueue);
      this.logger.log(`Updated email status: ${emailId} -> ${status}`);

      return updatedEmailQueue;
    } catch (error) {
      this.logger.error('Error updating email status:', error);
      throw new Error('Failed to update email status');
    }
  }

  /**
   * Xóa email queue entry
   */
  async deleteEmailQueue(emailId: number): Promise<void> {
    try {
      await this.emailQueueRepository.delete(emailId);
      this.logger.log(`Deleted email queue entry: ${emailId}`);
    } catch (error) {
      this.logger.error('Error deleting email queue entry:', error);
      throw new Error('Failed to delete email queue entry');
    }
  }

  /**
   * Lấy email queue theo ID
   */
  async getEmailQueueById(emailId: number): Promise<EmailQueue | null> {
    try {
      return await this.emailQueueRepository.findOne({
        where: { id: emailId } as any,
      });
    } catch (error) {
      this.logger.error('Error getting email queue by ID:', error);
      throw new Error('Failed to get email queue by ID');
    }
  }

  /**
   * Lấy email queue theo email type
   */
  async getEmailsByType(emailType: EmailType): Promise<EmailQueue[]> {
    try {
      return await this.emailQueueRepository.find({
        where: { email_type: emailType },
        order: { created_at: 'DESC' },
      });
    } catch (error) {
      this.logger.error('Error getting emails by type:', error);
      throw new Error('Failed to get emails by type');
    }
  }

  /**
   * Lấy thống kê email queue
   */
  async getEmailQueueStats(): Promise<{
    total: number;
    pending: number;
    sent: number;
    failed: number;
    cancelled: number;
  }> {
    try {
      const [total, pending, sent, failed, cancelled] = await Promise.all([
        this.emailQueueRepository.count(),
        this.emailQueueRepository.count({
          where: { status: EmailStatus.PENDING },
        }),
        this.emailQueueRepository.count({
          where: { status: EmailStatus.SENT },
        }),
        this.emailQueueRepository.count({
          where: { status: EmailStatus.FAILED },
        }),
        this.emailQueueRepository.count({
          where: { status: EmailStatus.CANCELLED },
        }),
      ]);

      return { total, pending, sent, failed, cancelled };
    } catch (error) {
      this.logger.error('Error getting email queue stats:', error);
      throw new Error('Failed to get email queue stats');
    }
  }

  /**
   * Generate welcome email content
   */
  private generateWelcomeEmailContent(
    email: string,
    fullName: string,
    password: string,
    loginUrl: string,
  ): string {
    return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Chào mừng bạn đến với Bright User Management System</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
    <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #2c3e50;">🎉 Chào mừng bạn!</h1>
        </div>
        
        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
            <h2 style="color: #2c3e50; margin-top: 0;">Xin chào ${fullName},</h2>
            
            <p>Tài khoản của bạn đã được tạo thành công trong hệ thống <strong>Bright User Management System</strong>.</p>
            
            <div style="background-color: #e8f5e8; padding: 15px; border-radius: 5px; margin: 20px 0;">
                <h3 style="color: #27ae60; margin-top: 0;">🔐 Thông tin đăng nhập:</h3>
                <p><strong>Email:</strong> ${email}</p>
                <p><strong>Mật khẩu:</strong> <code style="background-color: #fff; padding: 5px; border-radius: 3px;">${password}</code></p>
            </div>
            
            <p><strong>⚠️ Lưu ý quan trọng:</strong> Vui lòng đổi mật khẩu sau khi đăng nhập lần đầu để bảo mật tài khoản.</p>
        </div>
        
        <div style="text-align: center; margin: 30px 0;">
            <a href="${loginUrl}" style="background-color: #3498db; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
                🚀 Đăng nhập ngay
            </a>
        </div>
        
        <div style="background-color: #fff3cd; padding: 15px; border-radius: 5px; margin-top: 20px;">
            <h4 style="color: #856404; margin-top: 0;">💡 Hướng dẫn sử dụng:</h4>
            <ul style="margin: 10px 0; padding-left: 20px;">
                <li>Đăng nhập bằng email và mật khẩu được cung cấp</li>
                <li>Truy cập trang cá nhân để cập nhật thông tin</li>
                <li>Tùy chỉnh cài đặt theo ý muốn</li>
                <li>Liên hệ hỗ trợ nếu cần trợ giúp</li>
            </ul>
        </div>
        
        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; text-align: center; color: #666;">
            <p>Trân trọng,<br><strong>Đội ngũ Bright User Management System</strong></p>
            <p style="font-size: 12px;">Email này được gửi tự động, vui lòng không trả lời.</p>
        </div>
    </div>
</body>
</html>
    `;
  }

  /**
   * Generate password reset email content
   */
  private generatePasswordResetEmailContent(
    resetToken: string,
    resetUrl: string,
  ): string {
    return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Đặt lại mật khẩu - Bright User Management System</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
    <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #2c3e50;">🔐 Đặt lại mật khẩu</h1>
        </div>
        
        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
            <p>Bạn đã yêu cầu đặt lại mật khẩu cho tài khoản của mình.</p>
            
            <div style="text-align: center; margin: 30px 0;">
                <a href="${resetUrl}?token=${resetToken}" style="background-color: #e74c3c; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
                    🔄 Đặt lại mật khẩu
                </a>
            </div>
            
            <p><strong>⚠️ Lưu ý:</strong> Link này có hiệu lực trong 24 giờ. Nếu bạn không yêu cầu đặt lại mật khẩu, vui lòng bỏ qua email này.</p>
        </div>
        
        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; text-align: center; color: #666;">
            <p>Trân trọng,<br><strong>Đội ngũ Bright User Management System</strong></p>
            <p style="font-size: 12px;">Email này được gửi tự động, vui lòng không trả lời.</p>
        </div>
    </div>
</body>
</html>
    `;
  }
}
