import { Controller, Post, Body, HttpCode, HttpStatus } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { AdminSeeder, AdminUserData } from '../seeders/admin.seeder';

export class SeedAdminDto {
  email: string;
  password: string;
  fullName: string;
}

@ApiTags('Seeder')
@Controller('seeder')
export class SeederController {
  constructor(private readonly adminSeeder: AdminSeeder) {}

  @Post('admin')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Seed admin user',
    description: 'Tạo admin user với Firebase',
  })
  @ApiResponse({
    status: 200,
    description: 'Admin user seeded successfully',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - invalid data',
  })
  async seedAdmin(@Body() seedAdminDto: SeedAdminDto) {
    const adminData: AdminUserData = {
      email: seedAdminDto.email,
      password: seedAdminDto.password,
      fullName: seedAdminDto.fullName,
    };

    return this.adminSeeder.seedAdmin(adminData);
  }

  @Post('admin/default')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Seed default admin user',
    description: 'Tạo admin user mặc định (<EMAIL>)',
  })
  @ApiResponse({
    status: 200,
    description: 'Default admin user seeded successfully',
  })
  async seedDefaultAdmin() {
    return this.adminSeeder.seedDefaultAdmin();
  }
}
