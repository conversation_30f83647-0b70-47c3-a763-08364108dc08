import { ApiProperty } from '@nestjs/swagger';
import {
  IsEmail,
  IsString,
  IsOptional,
  IsEnum,
  IsBoolean,
} from 'class-validator';
import { Role } from '../../common/enum/role.enum';

export class CreateUserDto {
  @ApiProperty({ description: 'User email', example: '<EMAIL>' })
  @IsEmail({}, { message: '<PERSON>ail không hợp lệ' })
  email: string;
  @ApiProperty({
    description: 'Firebase ID của user',
    example: 'firebase_uid_here',
  })
  @IsString({ message: 'Firebase ID phải là chuỗi' })
  firebaseId: string;
  @ApiProperty({
    description: 'User role',
    example: 'USER',
    enum: ['USER', 'ADMIN'],
    required: false,
  })
  @IsOptional()
  @IsEnum(Role)
  role?: Role;
  @ApiProperty({
    description: 'User active status',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}
