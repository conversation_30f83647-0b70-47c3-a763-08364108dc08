/**
 * Test Authentication Module
 *
 * <PERSON><PERSON>ch sử dụng:
 * 1. Chạy server: npm run start:dev
 * 2. Sử dụng các request examples bên dướ<PERSON> để test
 */

import axios from 'axios';

const BASE_URL = 'http://localhost:3000';

// Test data
const testFirebaseToken = 'your_firebase_id_token_here'; // Thay bằng token thật từ Firebase

/**
 * Test 1: Verify Firebase Token
 */
export const testVerifyToken = async () => {
  try {
    console.log('🧪 Testing: Verify Firebase Token');

    const response = await axios.post(`${BASE_URL}/auth/verify`, {
      token: testFirebaseToken,
    });

    console.log('✅ Success:', response.data);
    return response.data;
  } catch (error) {
    console.error('❌ Error:', error.response?.data || error.message);
    return null;
  }
};

/**
 * Test 2: Get User Profile (requires authentication)
 */
export const testGetProfile = async () => {
  try {
    console.log('🧪 Testing: Get User Profile');

    const response = await axios.get(`${BASE_URL}/auth/profile`, {
      headers: {
        Authorization: `Bearer ${testFirebaseToken}`,
      },
    });

    console.log('✅ Success:', response.data);
    return response.data;
  } catch (error) {
    console.error('❌ Error:', error.response?.data || error.message);
    return null;
  }
};

/**
 * Test 3: Get User Permissions
 */
export const testGetPermissions = async () => {
  try {
    console.log('🧪 Testing: Get User Permissions');

    const response = await axios.get(`${BASE_URL}/auth/permissions`, {
      headers: {
        Authorization: `Bearer ${testFirebaseToken}`,
      },
    });

    console.log('✅ Success:', response.data);
    return response.data;
  } catch (error) {
    console.error('❌ Error:', error.response?.data || error.message);
    return null;
  }
};

/**
 * Test 4: Admin Only Endpoint
 */
export const testAdminOnly = async () => {
  try {
    console.log('🧪 Testing: Admin Only Endpoint');

    const response = await axios.get(`${BASE_URL}/auth/admin-only`, {
      headers: {
        Authorization: `Bearer ${testFirebaseToken}`,
      },
    });

    console.log('✅ Success:', response.data);
    return response.data;
  } catch (error) {
    console.error('❌ Error:', error.response?.data || error.message);
    return null;
  }
};

/**
 * Test 5: Public Endpoint (no auth required)
 */
export const testPublicEndpoint = async () => {
  try {
    console.log('🧪 Testing: Public Endpoint');

    const response = await axios.get(`${BASE_URL}/auth/public`);

    console.log('✅ Success:', response.data);
    return response.data;
  } catch (error) {
    console.error('❌ Error:', error.response?.data || error.message);
    return null;
  }
};

/**
 * Test 6: Protected Endpoint (auth required)
 */
export const testProtectedEndpoint = async () => {
  try {
    console.log('🧪 Testing: Protected Endpoint');

    const response = await axios.get(`${BASE_URL}/auth/protected`, {
      headers: {
        Authorization: `Bearer ${testFirebaseToken}`,
      },
    });

    console.log('✅ Success:', response.data);
    return response.data;
  } catch (error) {
    console.error('❌ Error:', error.response?.data || error.message);
    return null;
  }
};

/**
 * Test 7: Logout
 */
export const testLogout = async () => {
  try {
    console.log('🧪 Testing: Logout');

    const response = await axios.post(
      `${BASE_URL}/auth/logout`,
      {},
      {
        headers: {
          Authorization: `Bearer ${testFirebaseToken}`,
        },
      },
    );

    console.log('✅ Success:', response.data);
    return response.data;
  } catch (error) {
    console.error('❌ Error:', error.response?.data || error.message);
    return null;
  }
};

/**
 * Run all tests
 */
export const runAllTests = async () => {
  console.log('🚀 Starting Authentication Tests...\n');

  // Test public endpoint first (no auth required)
  await testPublicEndpoint();
  console.log('');

  // Test protected endpoints (require auth)
  await testVerifyToken();
  console.log('');

  await testGetProfile();
  console.log('');

  await testGetPermissions();
  console.log('');

  await testProtectedEndpoint();
  console.log('');

  await testAdminOnly();
  console.log('');

  await testLogout();
  console.log('');

  console.log('🏁 All tests completed!');
};

// Export for manual testing
export default {
  testVerifyToken,
  testGetProfile,
  testGetPermissions,
  testAdminOnly,
  testPublicEndpoint,
  testProtectedEndpoint,
  testLogout,
  runAllTests,
};
