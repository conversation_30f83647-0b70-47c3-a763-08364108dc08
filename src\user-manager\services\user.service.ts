import {
  Injectable,
  Logger,
  ConflictException,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '../entities/user.entity';
import { UserProfile } from '../entities/user-profile.entity';
import { UpdateUserDto, CreateUserWithSettingsDto } from '../dto/user.dto';
import { QuickCreateUserDto } from '../dto/quick-create.dto';
import { FirebaseService } from './firebase.service';
import { UserSettingsService } from './user-settings.service';
import { EmailQueueService } from './email-queue.service';
import { PasswordService } from './password.service';
import { Role } from '../../common/enum/role.enum';
import { RoleEntity } from '../entities/role.entity';

@Injectable()
export class UserService {
  private readonly logger = new Logger(UserService.name);

  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(UserProfile)
    private readonly userProfileRepository: Repository<UserProfile>,
    private readonly firebaseService: FirebaseService,
    private readonly userSettingsService: UserSettingsService,
    private readonly emailQueueService: EmailQueueService,
    private readonly passwordService: PasswordService,
  ) {}

  /**
   * Tạo user với Firebase integration
   */
  async createUserWithFirebase(createUserDto: CreateUserWithSettingsDto) {
    try {
      // Check if user already exists
      const existingUser = await this.userRepository.findOne({
        where: [
          { email: createUserDto.email },
          { firebaseId: createUserDto.firebaseId },
        ],
      });

      if (existingUser) {
        throw new ConflictException(
          'User already exists with this email or Firebase ID',
        );
      }

      // Verify Firebase user exists
      const firebaseUser = await this.firebaseService.getUserByUid(
        createUserDto.firebaseId,
      );

      const roleRepo = this.userRepository.manager.getRepository(RoleEntity);
      const role = await roleRepo.findOne({
        where: { name: createUserDto.role || 'user' },
      });
      if (!role) throw new BadRequestException('Role not found');

      // Create user in database
      const user = this.userRepository.create({
        email: createUserDto.email,
        firebaseId: createUserDto.firebaseId,
        role,
        is_active: createUserDto.is_active,
      });

      const savedUser = await this.userRepository.save(user);

      // Create user profile
      const userProfile = this.userProfileRepository.create({
        user: savedUser,
        full_name: firebaseUser.displayName || '',
        avatar_url: firebaseUser.photoURL || '',
      });

      await this.userProfileRepository.save(userProfile);

      // Create default settings
      const customSettings = createUserDto.customSettings || {};
      const userSettings = await this.userSettingsService.createDefaultSettings(
        savedUser,
        // Chuyển đổi Record<string, string> thành mảng object
        Object.entries(customSettings).map(([key, value]) => ({
          key,
          value,
          // Có thể bổ sung description/datatype nếu cần
        })),
      );

      // Set custom claims in Firebase
      await this.firebaseService.setCustomClaims(createUserDto.firebaseId, {
        role: createUserDto.role,
        userId: savedUser.id.toString(),
      });

      this.logger.log(
        `Successfully created user with Firebase: ${savedUser.id}`,
      );

      return {
        success: true,
        user: savedUser,
        profile: userProfile,
        settings: userSettings,
        firebase: {
          uid: firebaseUser.uid,
          email: firebaseUser.email,
          displayName: firebaseUser.displayName,
        },
      };
    } catch (error) {
      this.logger.error('Error creating user with Firebase:', error);
      throw error;
    }
  }

  /**
   * Quick create user với auto password generation
   */
  async quickCreateUser(quickCreateUserDto: QuickCreateUserDto) {
    try {
      // Check if user already exists
      const existingUser = await this.userRepository.findOne({
        where: { email: quickCreateUserDto.email },
      });

      if (existingUser) {
        throw new ConflictException('User already exists with this email');
      }

      // Generate password
      const generatedPassword = this.passwordService.generatePassword();

      // Create Firebase user
      const firebaseUser = await this.firebaseService.createUser({
        email: quickCreateUserDto.email,
        password: generatedPassword,
        displayName: quickCreateUserDto.fullName,
      });

      const roleRepo = this.userRepository.manager.getRepository(RoleEntity);
      const role = await roleRepo.findOne({
        where: { name: quickCreateUserDto.role || 'user' },
      });
      if (!role) throw new BadRequestException('Role not found');

      // Create user in database
      const user = this.userRepository.create({
        email: quickCreateUserDto.email,
        firebaseId: firebaseUser.uid,
        role,
        is_active: true,
      });

      const savedUser = await this.userRepository.save(user);

      // Create user profile
      const userProfile = this.userProfileRepository.create({
        user: savedUser,
        full_name: quickCreateUserDto.fullName,
        phone_number: quickCreateUserDto.phone,
        avatar_url: quickCreateUserDto.avatar,
        company: quickCreateUserDto.company,
        occupation: quickCreateUserDto.jobTitle,
      });

      await this.userProfileRepository.save(userProfile);

      // Create default settings
      const userSettings =
        await this.userSettingsService.createDefaultSettings(savedUser);

      // Set custom claims in Firebase
      await this.firebaseService.setCustomClaims(firebaseUser.uid, {
        role: quickCreateUserDto.role || Role.USER,
        userId: savedUser.id.toString(),
      });

      // Create email queue entry if requested
      let emailQueue: any = null;
      if (quickCreateUserDto.sendWelcomeEmail) {
        emailQueue = await this.emailQueueService.createWelcomeEmail(
          quickCreateUserDto.email,
          quickCreateUserDto.fullName,
          generatedPassword,
          process.env.LOGIN_URL || 'https://your-app.com/login',
        );
      }

      this.logger.log(
        `Quick created user: ${savedUser.id} with Firebase UID: ${firebaseUser.uid}`,
      );

      return {
        success: true,
        user: {
          id: savedUser.id,
          email: savedUser.email,
          firebaseId: savedUser.firebaseId,
          role: savedUser.role.name, // Return role as string
          is_active: savedUser.is_active,
        },
        profile: {
          full_name: userProfile.full_name,
          phone_number: userProfile.phone_number,
          avatar_url: userProfile.avatar_url,
          company: userProfile.company,
          occupation: userProfile.occupation,
        },
        settings: userSettings,
        password: generatedPassword,
        emailQueue: emailQueue
          ? {
              id: emailQueue.id,
              status: emailQueue.status,
              email_type: emailQueue.email_type,
            }
          : null,
        message:
          'User created successfully. Welcome email has been queued for sending.',
      };
    } catch (error) {
      this.logger.error('Error in quick create user:', error);

      // Rollback Firebase user if database creation fails
      if (
        error.message.includes('User already exists') &&
        !error.message.includes('Firebase')
      ) {
        // Try to delete Firebase user if it was created
        try {
          // Add Firebase user deletion logic here if needed
        } catch (rollbackError) {
          this.logger.error('Error rolling back Firebase user:', rollbackError);
        }
      }

      throw error;
    }
  }

  /**
   * Tìm user theo ID
   */
  async findOne(id: string) {
    const user = await this.userRepository.findOne({ where: { id } });
    if (!user) {
      throw new NotFoundException(`User with ID ${id} not found`);
    }
    return user;
  }

  /**
   * Tìm user theo Firebase ID
   */
  async findByFirebaseId(firebaseId: string) {
    const user = await this.userRepository.findOne({ where: { firebaseId } });
    if (!user) {
      throw new NotFoundException(
        `User with Firebase ID ${firebaseId} not found`,
      );
    }
    return user;
  }

  /**
   * Cập nhật user
   */
  async update(id: string, updateUserDto: UpdateUserDto) {
    const user = await this.findOne(id);

    Object.assign(user, updateUserDto);

    const updatedUser = await this.userRepository.save(user);

    // Update Firebase custom claims if role changed
    if (updateUserDto.role) {
      await this.firebaseService.setCustomClaims(user.firebaseId, {
        role: updateUserDto.role,
        userId: user.id.toString(),
      });
    }

    return updatedUser;
  }

  /**
   * Xóa user
   */
  async remove(id: string) {
    const user = await this.findOne(id);

    // Delete from Firebase
    await this.firebaseService.deleteUser(user.firebaseId);

    // Delete from database
    await this.userRepository.remove(user);

    return { success: true, message: 'User deleted successfully' };
  }

  /**
   * Lấy tất cả users
   */
  async findAll() {
    return await this.userRepository.find({
      relations: ['profile', 'settings'],
    });
  }
}
