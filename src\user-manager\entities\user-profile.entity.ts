import { <PERSON><PERSON><PERSON>, Column, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm';
import { User } from './user.entity'; // Đ<PERSON>m bảo bạn import đúng User Entity
import { Type } from 'class-transformer';
import { IsOptional, IsString, Length, IsDate, IsUrl } from 'class-validator';
import { BaseEntity } from '../../common/base/base.entity';

@Entity('user_profiles')
export class UserProfile extends BaseEntity {
  @OneToOne(() => User)
  @JoinColumn({ name: 'user_id' })
  user: User; // Quan hệ 1:1 với bảng users

  @Column({ type: 'varchar', length: 255, nullable: true })
  @IsOptional()
  full_name: string; // Tương ứng với VARCHAR(255)

  @Column({ type: 'text', nullable: true })
  @IsOptional()
  avatar_url: string; // Tương ứng với TEXT

  @Column({ type: 'varchar', length: 20, nullable: true })
  @IsOptional()
  phone_number: string; // Tương ứng với VARCHAR(20)

  @Column({ type: 'timestamptz', nullable: true })
  @IsOptional()
  last_login: Date; // Tương ứng với TIMESTAMPTZ

  @Column({ nullable: true })
  @IsOptional()
  @IsString()
  @Length(5, 100, { message: 'Address must be between 5 and 100 characters' })
  address1?: string;

  @Column({ nullable: true })
  @IsOptional()
  @IsString()
  @Length(5, 100, { message: 'Address must be between 5 and 100 characters' })
  address2?: string;

  @Column({ nullable: true })
  @IsOptional()
  @IsString()
  country?: string;

  @Column({ nullable: true, name: 'dob' })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  dateOfBirth?: Date;

  @Column({ nullable: true })
  @IsOptional()
  @IsUrl({}, { message: 'Invalid profile picture URL' })
  avatarImage?: string;

  @Column({ nullable: true })
  @IsOptional()
  @IsUrl({}, { message: 'Invalid profile picture URL' })
  coverImage?: string;

  @Column({ nullable: true })
  @IsOptional()
  @IsUrl({}, { message: 'Invalid Facebook profile URL' })
  facebookUrl?: string;

  @Column({ nullable: true })
  @IsOptional()
  @IsUrl({}, { message: 'Invalid Twitter profile URL' })
  twitterUrl?: string;

  @Column({ nullable: true })
  @IsOptional()
  @IsUrl({}, { message: 'Invalid LinkedIn profile URL' })
  linkedinUrl?: string;

  @Column({ nullable: true })
  @IsOptional()
  @IsString()
  company?: string;

  @Column({ nullable: true })
  @IsOptional()
  @IsString()
  occupation?: string;

  @Column({ nullable: true })
  @IsOptional()
  @IsString()
  bio?: string;

  @Column({ nullable: true })
  @IsOptional()
  @IsString()
  city?: string;

  @Column({ nullable: true })
  @IsOptional()
  @IsString()
  state?: string;

  @Column({ nullable: true })
  @IsOptional()
  @IsString()
  zipCode?: string;

  @Column({ nullable: true })
  @IsOptional()
  @IsString()
  gender?: string;
}
