import { ApiProperty } from '@nestjs/swagger';
import {
  IsEmail,
  IsString,
  IsOptional,
  IsEnum,
  IsBoolean,
} from 'class-validator';
import { Role } from '../../common/enum/role.enum';
import { CreateUserManagerDto } from './create-user-manager.dto';

export class CreateUserWithSettingsDto extends CreateUserManagerDto {
  @ApiProperty({
    description: 'Theme setting',
    required: false,
    default: 'light',
  })
  @IsString()
  @IsOptional()
  theme?: string = 'light';
  @ApiProperty({
    description: 'Language setting',
    required: false,
    default: 'vi',
  })
  @IsString()
  @IsOptional()
  language?: string = 'vi';
  @ApiProperty({
    description: 'Notifications enabled',
    required: false,
    default: true,
  })
  @IsBoolean()
  @IsOptional()
  notificationsEnabled?: boolean = true;
  @ApiProperty({
    description: 'Email notifications enabled',
    required: false,
    default: true,
  })
  @IsBoolean()
  @IsOptional()
  emailNotifications?: boolean = true;
  @ApiProperty({
    description: 'Push notifications enabled',
    required: false,
    default: true,
  })
  @IsBoolean()
  @IsOptional()
  pushNotifications?: boolean = true;
  @ApiProperty({
    description: 'Auto save enabled',
    required: false,
    default: true,
  })
  @IsBoolean()
  @IsOptional()
  autoSave?: boolean = true;
  @ApiProperty({
    description: 'Sound enabled',
    required: false,
    default: true,
  })
  @IsBoolean()
  @IsOptional()
  soundEnabled?: boolean = true;
  @ApiProperty({
    description: 'Timezone',
    required: false,
    default: 'Asia/Ho_Chi_Minh',
  })
  @IsString()
  @IsOptional()
  timezone?: string = 'Asia/Ho_Chi_Minh';
  @ApiProperty({
    description: 'Date format',
    required: false,
    default: 'DD/MM/YYYY',
  })
  @IsString()
  @IsOptional()
  dateFormat?: string = 'DD/MM/YYYY';
  @ApiProperty({
    description: 'Time format',
    required: false,
    default: '24',
  })
  @IsString()
  @IsOptional()
  timeFormat?: string = '24';
}
