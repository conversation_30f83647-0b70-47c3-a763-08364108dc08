import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In, Like } from 'typeorm';
import { User } from '../entities/user.entity';
import { UserProfile } from '../entities/user-profile.entity';
import {
  ChatUserInfoDto,
  ChatUserProfileDto,
} from '../dto/chat-user-info.dto';
import { RedisService } from '../../common/services/redis.service';

@Injectable()
export class ChatApiService {
  private readonly logger = new Logger(ChatApiService.name);
  private readonly CACHE_TTL = 300; // 5 minutes

  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(UserProfile)
    private readonly userProfileRepository: Repository<UserProfile>,
    private readonly redisService: RedisService,
  ) {}

  /**
   * Get user information by ID with caching
   */
  async getUserById(userId: string): Promise<ChatUserInfoDto | null> {
    try {
      // Try cache first
      const cacheKey = `chat:user:${userId}`;
      const cached = await this.redisService.get(cacheKey);
      if (cached) {
        this.logger.debug(`Cache hit for user: ${userId}`);
        return JSON.parse(cached);
      }

      // Query database
      const user = await this.userRepository.findOne({
        where: { id: userId },
        relations: ['role'],
      });

      if (!user) {
        return null;
      }

      const profile = await this.userProfileRepository.findOne({
        where: { user: { id: userId } },
      });

      const userInfo: ChatUserInfoDto = {
        userId: user.id,
        email: user.email,
        displayName: profile?.full_name || user.email.split('@')[0],
        role: user.role?.name || 'USER',
        emailVerified: true, // Assuming Firebase handles this
        enabled: user.is_active,
        avatarUrl: profile?.avatar_url || profile?.avatarImage,
        phoneNumber: profile?.phone_number,
        company: profile?.company,
        occupation: profile?.occupation,
      };

      // Cache the result
      await this.redisService.setex(cacheKey, this.CACHE_TTL, JSON.stringify(userInfo));
      this.logger.debug(`Cached user info for: ${userId}`);

      return userInfo;
    } catch (error) {
      this.logger.error(`Error fetching user ${userId}:`, error);
      return null;
    }
  }

  /**
   * Get multiple users by their IDs
   */
  async getUsersByIds(userIds: string[]): Promise<ChatUserInfoDto[]> {
    try {
      this.logger.debug(`Fetching ${userIds.length} users`);

      const users = await this.userRepository.find({
        where: { id: In(userIds) },
        relations: ['role'],
      });

      const profiles = await this.userProfileRepository.find({
        where: { user: { id: In(userIds) } },
        relations: ['user'],
      });

      // Create a map for quick profile lookup
      const profileMap = new Map<string, UserProfile>();
      profiles.forEach(profile => {
        profileMap.set(profile.user.id, profile);
      });

      const result: ChatUserInfoDto[] = users.map(user => {
        const profile = profileMap.get(user.id);
        return {
          userId: user.id,
          email: user.email,
          displayName: profile?.full_name || user.email.split('@')[0],
          role: user.role?.name || 'USER',
          emailVerified: true,
          enabled: user.is_active,
          avatarUrl: profile?.avatar_url || profile?.avatarImage,
          phoneNumber: profile?.phone_number,
          company: profile?.company,
          occupation: profile?.occupation,
        };
      });

      this.logger.debug(`Found ${result.length} users out of ${userIds.length} requested`);
      return result;
    } catch (error) {
      this.logger.error('Error fetching users by IDs:', error);
      return [];
    }
  }

  /**
   * Search users by username or display name
   */
  async searchUsers(query: string, limit: number): Promise<ChatUserInfoDto[]> {
    try {
      this.logger.debug(`Searching users with query: ${query} (limit: ${limit})`);

      // Search by email or profile full_name
      const users = await this.userRepository
        .createQueryBuilder('user')
        .leftJoinAndSelect('user.role', 'role')
        .leftJoinAndSelect('user_profiles', 'profile', 'profile.user_id = user.id')
        .where('user.email ILIKE :query', { query: `%${query}%` })
        .orWhere('profile.full_name ILIKE :query', { query: `%${query}%` })
        .andWhere('user.is_active = :active', { active: true })
        .limit(limit)
        .getMany();

      const userIds = users.map(user => user.id);
      const profiles = await this.userProfileRepository.find({
        where: { user: { id: In(userIds) } },
        relations: ['user'],
      });

      const profileMap = new Map<string, UserProfile>();
      profiles.forEach(profile => {
        profileMap.set(profile.user.id, profile);
      });

      const result: ChatUserInfoDto[] = users.map(user => {
        const profile = profileMap.get(user.id);
        return {
          userId: user.id,
          email: user.email,
          displayName: profile?.full_name || user.email.split('@')[0],
          role: user.role?.name || 'USER',
          emailVerified: true,
          enabled: user.is_active,
          avatarUrl: profile?.avatar_url || profile?.avatarImage,
          phoneNumber: profile?.phone_number,
          company: profile?.company,
          occupation: profile?.occupation,
        };
      });

      this.logger.debug(`Found ${result.length} users for query: ${query}`);
      return result;
    } catch (error) {
      this.logger.error(`Error searching users with query: ${query}`, error);
      return [];
    }
  }

  /**
   * Check if user exists
   */
  async userExists(userId: string): Promise<boolean> {
    try {
      const cacheKey = `chat:exists:${userId}`;
      const cached = await this.redisService.get(cacheKey);
      if (cached !== null) {
        return cached === 'true';
      }

      const count = await this.userRepository.count({
        where: { id: userId, is_active: true },
      });

      const exists = count > 0;
      await this.redisService.setex(cacheKey, this.CACHE_TTL, exists.toString());

      return exists;
    } catch (error) {
      this.logger.error(`Error checking if user exists: ${userId}`, error);
      return false;
    }
  }

  /**
   * Validate multiple user IDs and return valid ones
   */
  async validateUserIds(userIds: string[]): Promise<string[]> {
    try {
      this.logger.debug(`Validating ${userIds.length} user IDs`);

      const users = await this.userRepository.find({
        where: { id: In(userIds), is_active: true },
        select: ['id'],
      });

      const validIds = users.map(user => user.id);
      this.logger.debug(`${validIds.length} out of ${userIds.length} user IDs are valid`);

      return validIds;
    } catch (error) {
      this.logger.error('Error validating user IDs:', error);
      return [];
    }
  }

  /**
   * Get user role
   */
  async getUserRole(userId: string): Promise<string | null> {
    try {
      const cacheKey = `chat:role:${userId}`;
      const cached = await this.redisService.get(cacheKey);
      if (cached) {
        return cached;
      }

      const user = await this.userRepository.findOne({
        where: { id: userId },
        relations: ['role'],
      });

      if (!user) {
        return null;
      }

      const role = user.role?.name || 'USER';
      await this.redisService.setex(cacheKey, this.CACHE_TTL, role);

      return role;
    } catch (error) {
      this.logger.error(`Error fetching user role for: ${userId}`, error);
      return null;
    }
  }

  /**
   * Get user profile with role
   */
  async getUserProfile(userId: string): Promise<ChatUserProfileDto | null> {
    try {
      const cacheKey = `chat:profile:${userId}`;
      const cached = await this.redisService.get(cacheKey);
      if (cached) {
        return JSON.parse(cached);
      }

      const user = await this.userRepository.findOne({
        where: { id: userId },
        relations: ['role'],
      });

      if (!user) {
        return null;
      }

      const profile = await this.userProfileRepository.findOne({
        where: { user: { id: userId } },
      });

      const userProfile: ChatUserProfileDto = {
        userId: user.id,
        email: user.email,
        displayName: profile?.full_name || user.email.split('@')[0],
        role: user.role?.name || 'USER',
        emailVerified: true,
        enabled: user.is_active,
      };

      await this.redisService.setex(cacheKey, this.CACHE_TTL, JSON.stringify(userProfile));

      return userProfile;
    } catch (error) {
      this.logger.error(`Error fetching user profile for: ${userId}`, error);
      return null;
    }
  }

  /**
   * Clear user cache when user data is updated
   */
  async evictUserCache(userId: string): Promise<void> {
    try {
      const keys = [
        `chat:user:${userId}`,
        `chat:exists:${userId}`,
        `chat:role:${userId}`,
        `chat:profile:${userId}`,
      ];

      await Promise.all(keys.map(key => this.redisService.del(key)));
      this.logger.debug(`Evicted cache for user: ${userId}`);
    } catch (error) {
      this.logger.error(`Error evicting cache for user: ${userId}`, error);
    }
  }
}
