import { ApiProperty } from '@nestjs/swagger';
import { TokenResponseDto } from './TokenResponse.dto';
import { UserResponseDto } from './UserResponse.dto';

export class AdminLoginResponseDto {
  @ApiProperty({ description: 'Success status', example: true })
  success: boolean;

  @ApiProperty({
    description: 'Response message',
    example: 'Admin login successful',
  })
  message: string;

  @ApiProperty({ description: 'Token data', type: TokenResponseDto })
  data: TokenResponseDto;

  @ApiProperty({ description: 'User information', type: UserResponseDto })
  user: UserResponseDto;
}
