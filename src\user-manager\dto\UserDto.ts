import { ApiProperty } from '@nestjs/swagger';

export class UserDto {
  @ApiProperty({
    description: 'User ID (UUID)',
    example: '123e4567-e89b-12d3-a456-************',
  })
  id: string;
  @ApiProperty({ description: 'User email', example: '<EMAIL>' })
  email: string;
  @ApiProperty({
    description: 'User role',
    example: 'USER',
    enum: ['USER', 'ADMIN'],
  })
  role: string;
  @ApiProperty({ description: 'User active status', example: true })
  isActive: boolean;
}
