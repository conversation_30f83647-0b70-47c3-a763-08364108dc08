import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { getStorage } from 'firebase-admin/storage';
import { v4 as uuidv4 } from 'uuid';

export interface UploadResult {
  url: string;
  path: string;
  filename: string;
  size: number;
  mimetype: string;
}

@Injectable()
export class FileUploadService {
  private readonly logger = new Logger(FileUploadService.name);
  private readonly bucket: any;

  constructor(private configService: ConfigService) {
    // Khởi tạo Firebase Storage bucket
    const projectId = this.configService.get<string>('FIREBASE_PROJECT_ID');
    if (!projectId) {
      throw new Error('FIREBASE_PROJECT_ID is not configured');
    }

    this.bucket = getStorage().bucket(`${projectId}.appspot.com`);
  }

  /**
   * Upload file lên Firebase Storage
   */
  async uploadFile(
    file: Express.Multer.File,
    folder: string = 'avatars',
    userId?: string,
  ): Promise<UploadResult> {
    try {
      // Validate file
      this.validateFile(file);

      // Tạo tên file unique
      const fileExtension = this.getFileExtension(file.originalname);
      const filename = `${folder}/${userId || 'temp'}/${uuidv4()}.${fileExtension}`;

      // Upload file lên Firebase Storage
      const fileBuffer = file.buffer;
      const fileUpload = this.bucket.file(filename);

      await fileUpload.save(fileBuffer, {
        metadata: {
          contentType: file.mimetype,
          metadata: {
            originalName: file.originalname,
            uploadedBy: userId?.toString() || 'unknown',
            uploadedAt: new Date().toISOString(),
          },
        },
      });

      // Tạo public URL
      const [url] = await fileUpload.getSignedUrl({
        action: 'read',
        expires: '03-01-2500', // URL không hết hạn
      });

      this.logger.log(`File uploaded successfully: ${filename}`);

      return {
        url,
        path: filename,
        filename: file.originalname,
        size: file.size,
        mimetype: file.mimetype,
      };
    } catch (error) {
      this.logger.error('Error uploading file:', error);
      throw new BadRequestException('Failed to upload file');
    }
  }

  /**
   * Upload avatar cho user
   */
  async uploadAvatar(
    file: Express.Multer.File,
    userId: string,
  ): Promise<UploadResult> {
    return this.uploadFile(file, 'avatars', userId);
  }

  /**
   * Xóa file từ Firebase Storage
   */
  async deleteFile(filePath: string): Promise<void> {
    try {
      await this.bucket.file(filePath).delete();
      this.logger.log(`File deleted successfully: ${filePath}`);
    } catch (error) {
      this.logger.error('Error deleting file:', error);
      throw new BadRequestException('Failed to delete file');
    }
  }

  /**
   * Validate file upload
   */
  private validateFile(file: Express.Multer.File): void {
    // Kiểm tra file có tồn tại không
    if (!file) {
      throw new BadRequestException('No file uploaded');
    }

    // Kiểm tra kích thước file (max 5MB)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      throw new BadRequestException('File size too large. Maximum size is 5MB');
    }

    // Kiểm tra loại file
    const allowedMimeTypes = [
      'image/jpeg',
      'image/jpg',
      'image/png',
      'image/gif',
      'image/webp',
    ];

    if (!allowedMimeTypes.includes(file.mimetype)) {
      throw new BadRequestException(
        'Invalid file type. Only JPEG, PNG, GIF, and WebP are allowed',
      );
    }
  }

  /**
   * Lấy extension của file
   */
  private getFileExtension(filename: string): string {
    return filename.split('.').pop()?.toLowerCase() || 'jpg';
  }

  /**
   * Tạo thumbnail cho avatar
   */
  async createAvatarThumbnail(
    file: Express.Multer.File,
    userId: string,
    size: number = 150,
  ): Promise<UploadResult> {
    try {
      // Validate file
      this.validateFile(file);

      // Tạo tên file thumbnail
      const fileExtension = this.getFileExtension(file.originalname);
      const filename = `avatars/${userId}/thumbnails/${uuidv4()}.${fileExtension}`;

      // TODO: Implement image resizing logic here
      // For now, we'll just upload the original file
      const fileBuffer = file.buffer;
      const fileUpload = this.bucket.file(filename);

      await fileUpload.save(fileBuffer, {
        metadata: {
          contentType: file.mimetype,
          metadata: {
            originalName: file.originalname,
            uploadedBy: userId.toString(),
            uploadedAt: new Date().toISOString(),
            isThumbnail: 'true',
            thumbnailSize: size.toString(),
          },
        },
      });

      // Tạo public URL
      const [url] = await fileUpload.getSignedUrl({
        action: 'read',
        expires: '03-01-2500',
      });

      this.logger.log(`Avatar thumbnail created: ${filename}`);

      return {
        url,
        path: filename,
        filename: file.originalname,
        size: file.size,
        mimetype: file.mimetype,
      };
    } catch (error) {
      this.logger.error('Error creating avatar thumbnail:', error);
      throw new BadRequestException('Failed to create avatar thumbnail');
    }
  }
}
