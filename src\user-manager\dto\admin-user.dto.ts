import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsOptional,
  IsString,
  IsEnum,
  IsNumber,
  IsIn,
  IsBoolean,
  IsArray,
  IsDateString,
  Min,
  ArrayNotEmpty,
  ArrayUnique,
} from 'class-validator';
import { Role } from '../../common/enum/role.enum';

export class AdminUserFilterDto {
  @ApiPropertyOptional({ description: 'Tìm kiếm theo email hoặc tên' })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({ description: 'Role (một role)' })
  @IsOptional()
  @IsEnum(Role)
  role?: Role;

  @ApiPropertyOptional({
    description: '<PERSON><PERSON> sách role (nhiều role)',
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @ArrayNotEmpty()
  @ArrayUnique()
  @IsEnum(Role, { each: true })
  roles?: Role[];

  @ApiPropertyOptional({
    description: 'Trạng thái',
    enum: ['active', 'locked'],
  })
  @IsOptional()
  @IsIn(['active', 'locked'])
  status?: 'active' | 'locked';

  @ApiPropertyOptional({ description: 'Email đã xác thực' })
  @IsOptional()
  @IsBoolean()
  is_email_verified?: boolean;

  @ApiPropertyOptional({ description: 'Ngày tạo từ (ISO)' })
  @IsOptional()
  @IsDateString()
  createdFrom?: string;

  @ApiPropertyOptional({ description: 'Ngày tạo đến (ISO)' })
  @IsOptional()
  @IsDateString()
  createdTo?: string;

  @ApiPropertyOptional({ description: 'Trang', default: 1 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  page?: number = 1;

  @ApiPropertyOptional({ description: 'Số lượng/trang', default: 20 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  limit?: number = 20;

  @ApiPropertyOptional({ description: 'Xuất file', enum: ['csv', 'excel'] })
  @IsOptional()
  @IsIn(['csv', 'excel'])
  export?: 'csv' | 'excel';
}

export class AdminUserUpdateDto {
  @ApiPropertyOptional({ description: 'Role' })
  @IsOptional()
  @IsEnum(Role)
  role?: Role;

  @ApiPropertyOptional({ description: 'Trạng thái hoạt động' })
  @IsOptional()
  @IsBoolean()
  is_active?: boolean;

  @ApiPropertyOptional({ description: 'Tên đầy đủ' })
  @IsOptional()
  @IsString()
  full_name?: string;
}
