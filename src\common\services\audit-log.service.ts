import { Inject, Injectable, Scope } from '@nestjs/common';
import { RabbitMQService } from './rabbitmq.service';
import {
  RABBITMQ_EXCHANGES,
  RABBITMQ_ROUTING_KEYS,
} from '../enum/rabbitmq.enum';
import { REQUEST } from '@nestjs/core';

@Injectable({ scope: Scope.REQUEST })
export class AuditLogService {
  constructor(
    @Inject(REQUEST) private readonly req: Request,
    private readonly rabbitMQService: RabbitMQService,
  ) {}

  public async sendUserAuditLog({
    userId,
    action,
    meta,
  }: {
    userId: string;
    action: string;
    meta?: any;
  }) {
    const getHeader = (h: string) => {
      const v = this.req.headers[h];
      if (Array.isArray(v)) return v[0];
      return v as string | undefined;
    };
    const ip = getHeader('x-client-ip') || '';
    const device = getHeader('x-client-device') || '';
    const timezone = getHeader('x-client-timezone') || 'UTC';
    const platform = getHeader('x-client-platform') || 'Win64';
    const location = getHeader('x-client-location') || '';
    const lang = getHeader('x-client-lang') || 'en';
    await this.rabbitMQService.publish(
      RABBITMQ_EXCHANGES.BRIGHT_TOPIC,
      RABBITMQ_ROUTING_KEYS.AUDIT_AUDIT,
      {
        userId,
        action,
        timestamp: new Date().toISOString(),
        ip: ip,
        device: device,
        timezone: timezone,
        platform: platform,
        location: location,
        lang: lang,
        meta: meta || null,
      },
    );
  }
}
