import { AsyncLocalStorage } from 'async_hooks';
import { Injectable, Scope } from '@nestjs/common';

export interface RequestContextData {
  ip?: string;
  device?: string;
  timezone?: string;
  platform?: string;
  location?: string;
  lang?: string;
}

@Injectable({ scope: Scope.REQUEST })
export class RequestContext {
  private readonly als = new AsyncLocalStorage<RequestContextData>();

  run(context: RequestContextData, callback: (...args: any[]) => void) {
    this.als.run(context, callback);
  }

  get(): RequestContextData | undefined {
    return this.als.getStore();
  }
}
