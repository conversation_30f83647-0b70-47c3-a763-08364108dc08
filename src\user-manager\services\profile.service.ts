import {
  Injectable,
  Logger,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '../entities/user.entity';
import { UserProfile } from '../entities/user-profile.entity';
import { FirebaseService } from './firebase.service';
import { FileUploadService } from './file-upload.service';
import { RedisService } from '../../common/services/redis.service';
import { MapperService } from '../../common/services/mapper.service';
import { RabbitMQService } from '../../common/services/rabbitmq.service';
import {
  RABBITMQ_EXCHANGES,
  RABBITMQ_ROUTING_KEYS,
} from '../../common/enum/rabbitmq.enum';

import { RequestContext } from '../../common/context/request-context';
import { ProfileResponseDto } from '../dto/ProfileResponse.dto';
import { UpdateProfileDto } from '../dto/UpdateProfile.dto';
import { UpdateAvatarDto } from '../dto/UpdateAvatar.dto';
import { ToggleAccountStatusDto } from '../dto/ToggleAccountStatus.dto';
import { DeactivateAccountDto } from '../dto/DeactivateAccount.dto';
import { ReactivateAccountDto } from '../dto/ReactivateAccount.dto';

@Injectable()
export class ProfileService {
  private readonly logger = new Logger(ProfileService.name);

  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(UserProfile)
    private readonly userProfileRepository: Repository<UserProfile>,
    private readonly firebaseService: FirebaseService,
    private readonly fileUploadService: FileUploadService,
    private readonly redisService: RedisService,
    private readonly mapperService: MapperService,
    private readonly rabbitMQService: RabbitMQService,
    private requestContext: RequestContext,
  ) {}

  /**
   * Helper: Send user audit log to RabbitMQ
   */
  private async sendUserAuditLog({
    userId,
    action,
    meta,
  }: {
    userId: string;
    action: string;
    meta?: any;
  }) {
    const context = this.requestContext.get() || {};
    await this.rabbitMQService.publish(
      RABBITMQ_EXCHANGES.BRIGHT_TOPIC,
      RABBITMQ_ROUTING_KEYS.AUDIT_USER,
      {
        userId,
        action,
        timestamp: new Date().toISOString(),
        ip: context.ip || null,
        device: context.device || null,
        timezone: context.timezone || null,
        platform: context.platform || null,
        location: context.location || null,
        lang: context.lang || null,
        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
        meta: meta || null,
      },
    );
  }

  /**
   * Get user profile (with 7-day cache)
   */
  async getUserProfile(userId: string): Promise<ProfileResponseDto> {
    try {
      // Try to get from cache first
      const cacheKey = this.redisService.generateUserProfileKey(userId);
      const cachedProfile =
        await this.redisService.get<ProfileResponseDto>(cacheKey);

      if (cachedProfile) {
        this.logger.log(`User profile for ${userId} retrieved from cache`);
        return cachedProfile;
      }

      // Verify user exists
      const user = await this.userRepository.findOne({ where: { id: userId } });
      if (!user) {
        throw new NotFoundException(`User with ID ${userId} not found`);
      }

      // Get user profile
      const userProfile = await this.userProfileRepository.findOne({
        where: { user: { id: userId } },
        relations: ['user'],
      });

      if (!userProfile) {
        throw new NotFoundException(`Profile not found for user ${userId}`);
      }

      const profileResponse = {
        success: true,
        message: 'Profile retrieved successfully',
        data: {
          user: {
            id: user.id,
            email: user.email,
            role: user.role?.name ?? '',
            is_active: user.is_active,
          },
          profile: {
            full_name: userProfile.full_name,
            phone_number: userProfile.phone_number,
            avatar_url: userProfile.avatar_url,
            company: userProfile.company,
            occupation: userProfile.occupation,
            bio: userProfile.bio,
            address1: userProfile.address1,
            address2: userProfile.address2,
            city: userProfile.city,
            state: userProfile.state,
            zipCode: userProfile.zipCode,
            country: userProfile.country,
            gender: userProfile.gender,
            dateOfBirth: userProfile.dateOfBirth,
          },
        },
      };

      // Cache the profile response for 7 days
      await this.redisService.set(cacheKey, profileResponse, { ttl: 604800 }); // 7 days

      this.logger.log(
        `User profile for ${userId} retrieved from database and cached for 7 days`,
      );
      return profileResponse;
    } catch (error) {
      this.logger.error(
        `Error getting user profile for user ${userId}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Update user profile
   */
  async updateProfile(userId: string, updateProfileDto: UpdateProfileDto) {
    try {
      // Verify user exists
      const user = await this.userRepository.findOne({ where: { id: userId } });
      if (!user) {
        throw new NotFoundException(`User with ID ${userId} not found`);
      }

      // Get or create user profile
      let userProfile = await this.userProfileRepository.findOne({
        where: { user: { id: userId } },
      });

      if (!userProfile) {
        userProfile = this.userProfileRepository.create({
          user,
          full_name: updateProfileDto.fullName || '',
          last_login: new Date(),
        });
      } else {
        // Update existing profile
        Object.assign(userProfile, {
          full_name: updateProfileDto.fullName || userProfile.full_name,
          phone_number:
            updateProfileDto.phoneNumber || userProfile.phone_number,
          company: updateProfileDto.company || userProfile.company,
          occupation: updateProfileDto.occupation || userProfile.occupation,
          bio: updateProfileDto.bio || userProfile.bio,
          address1: updateProfileDto.address1 || userProfile.address1,
          address2: updateProfileDto.address2 || userProfile.address2,
          city: updateProfileDto.city || userProfile.city,
          state: updateProfileDto.state || userProfile.state,
          zipCode: updateProfileDto.zipCode || userProfile.zipCode,
          country: updateProfileDto.country || userProfile.country,
          gender: updateProfileDto.gender || userProfile.gender,
          dateOfBirth: updateProfileDto.dateOfBirth || userProfile.dateOfBirth,
          last_login: new Date(),
        });
      }

      const savedProfile = await this.userProfileRepository.save(userProfile);

      // Invalidate cache
      const cacheKey = this.redisService.generateUserKey(userId);
      await this.redisService.del(cacheKey);

      this.logger.log(`Profile updated for user ${userId}`);

      // Gửi audit log qua RabbitMQ
      await this.sendUserAuditLog({
        userId,
        action: 'UPDATE_PROFILE',
        meta: updateProfileDto,
      });

      return {
        success: true,
        message: 'Profile updated successfully',
        data: {
          user: {
            id: user.id,
            email: user.email,
            role: user.role?.name ?? '',
            is_active: user.is_active,
          },
          profile: {
            full_name: savedProfile.full_name,
            phone_number: savedProfile.phone_number,
            company: savedProfile.company,
            occupation: savedProfile.occupation,
            bio: savedProfile.bio,
            address1: savedProfile.address1,
            address2: savedProfile.address2,
            city: savedProfile.city,
            state: savedProfile.state,
            zipCode: savedProfile.zipCode,
            country: savedProfile.country,
            gender: savedProfile.gender,
            dateOfBirth: savedProfile.dateOfBirth,
          },
        },
      };
    } catch (error) {
      this.logger.error(`Error updating profile for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Update avatar URL
   */
  async updateAvatar(userId: string, updateAvatarDto: UpdateAvatarDto) {
    try {
      // Verify user exists
      const user = await this.userRepository.findOne({ where: { id: userId } });
      if (!user) {
        throw new NotFoundException(`User with ID ${userId} not found`);
      }

      // Get or create user profile
      let userProfile = await this.userProfileRepository.findOne({
        where: { user: { id: userId } },
      });

      if (!userProfile) {
        userProfile = this.userProfileRepository.create({
          user,
          full_name: user.email.split('@')[0],
          avatar_url: updateAvatarDto.avatarUrl,
          last_login: new Date(),
        });
      } else {
        userProfile.avatar_url = updateAvatarDto.avatarUrl || '';
      }

      const savedProfile = await this.userProfileRepository.save(userProfile);

      // Invalidate cache
      const cacheKey = this.redisService.generateUserKey(userId);
      await this.redisService.del(cacheKey);

      this.logger.log(`Avatar updated for user ${userId}`);

      return {
        success: true,
        message: 'Avatar updated successfully',
        data: {
          avatarUrl: savedProfile.avatar_url,
        },
      };
    } catch (error) {
      this.logger.error(`Error updating avatar for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Upload avatar file
   */
  async uploadAvatar(userId: string, file: Express.Multer.File) {
    try {
      // Verify user exists
      const [user] = await Promise.all([
        this.userRepository.findOne({ where: { id: userId } }),
      ]);
      if (!user) {
        throw new NotFoundException(`User with ID ${userId} not found`);
      }

      // Upload file to Firebase Storage
      const uploadResult = await this.fileUploadService.uploadAvatar(
        file,
        userId,
      );

      // Update user profile with new avatar URL
      let userProfile = await this.userProfileRepository.findOne({
        where: { user: { id: userId } },
      });

      if (!userProfile) {
        userProfile = this.userProfileRepository.create({
          user,
          full_name: user.email.split('@')[0],
          avatar_url: uploadResult.url,
          last_login: new Date(),
        });
      } else {
        userProfile.avatar_url = uploadResult.url;
      }

      await this.userProfileRepository.save(userProfile);

      // Invalidate cache
      const cacheKey = this.redisService.generateUserKey(userId);
      await this.redisService.del(cacheKey);

      this.logger.log(`Avatar uploaded for user ${userId}`);

      return this.mapperService.createAvatarUploadResponse(
        uploadResult.url,
        uploadResult.filename,
        file.size,
        file.mimetype,
      );
    } catch (error) {
      this.logger.error(`Error uploading avatar for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Toggle account status
   */
  async toggleAccountStatus(userId: string, toggleDto: ToggleAccountStatusDto) {
    try {
      // Verify user exists
      const user = await this.userRepository.findOne({ where: { id: userId } });
      if (!user) {
        throw new NotFoundException(`User with ID ${userId} not found`);
      }

      // Update account status
      user.is_active = toggleDto.isActive;
      const updatedUser = await this.userRepository.save(user);

      // Invalidate cache
      const cacheKey = this.redisService.generateUserKey(userId);
      await this.redisService.del(cacheKey);

      this.logger.log(
        `Account status toggled for user ${userId}: ${toggleDto.isActive}`,
      );

      return this.mapperService.createAccountStatusResponse(
        updatedUser.is_active,
        'Account status updated successfully',
      );
    } catch (error) {
      this.logger.error(
        `Error toggling account status for user ${userId}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Deactivate account
   */
  async deactivateAccount(userId: string, deactivateDto: DeactivateAccountDto) {
    try {
      if (!deactivateDto.confirm) {
        throw new BadRequestException(
          'Confirmation required to deactivate account',
        );
      }

      // Verify user exists
      const user = await this.userRepository.findOne({ where: { id: userId } });
      if (!user) {
        throw new NotFoundException(`User with ID ${userId} not found`);
      }

      // Deactivate account
      user.is_active = false;
      await this.userRepository.save(user);

      const cacheKey = this.redisService.generateUserKey(userId);
      await this.redisService.del(cacheKey);

      this.logger.log(
        `Account deactivated for user ${userId}. Reason: ${deactivateDto.reason || 'No reason provided'}`,
      );

      return this.mapperService.createAccountStatusResponse(
        false,
        'Account deactivated successfully',
      );
    } catch (error) {
      this.logger.error(
        `Error deactivating account for user ${userId}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Reactivate account
   */
  async reactivateAccount(userId: string, reactivateDto: ReactivateAccountDto) {
    try {
      // Verify user exists
      const user = await this.userRepository.findOne({ where: { id: userId } });
      if (!user) {
        throw new NotFoundException(`User with ID ${userId} not found`);
      }

      // Reactivate account
      user.is_active = true;
      await this.userRepository.save(user);
      // Invalidate cache
      const cacheKey = this.redisService.generateUserKey(userId);
      await this.redisService.del(cacheKey);

      this.logger.log(
        `Account reactivated for user ${userId}. Reason: ${reactivateDto.reason || 'No reason provided'}`,
      );

      return this.mapperService.createAccountStatusResponse(
        true,
        'Account reactivated successfully',
      );
    } catch (error) {
      this.logger.error(
        `Error reactivating account for user ${userId}:`,
        error,
      );
      throw error;
    }
  }
}
