import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional } from 'class-validator';

export class GenerateMFASecretDto {
  @ApiProperty({
    description: 'User ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @IsString()
  userId: string;
}

export class EnableMFADto {
  @ApiProperty({
    description: 'User ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @IsString()
  userId: string;

  @ApiProperty({
    description: 'MFA token từ authentication app',
    example: '123456',
  })
  @IsString()
  token: string;
}

export class DisableMFADto {
  @ApiProperty({
    description: 'User ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @IsString()
  userId: string;

  @ApiProperty({
    description: 'MFA token từ authentication app',
    example: '123456',
  })
  @IsString()
  token: string;
}

export class VerifyMFADto {
  @ApiProperty({
    description: 'User ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @IsString()
  userId: string;

  @ApiProperty({
    description: 'MFA token từ authentication app',
    example: '123456',
  })
  @IsString()
  token: string;
}

export class SaveMFASecretDto {
  @ApiProperty({
    description: 'User ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @IsString()
  userId: string;

  @ApiProperty({
    description: 'MFA secret key',
    example: 'JBSWY3DPEHPK3PXP',
  })
  @IsString()
  secret: string;
}

export class GetMFAStatusDto {
  @ApiProperty({
    description: 'User ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @IsString()
  userId: string;
}

export class MFASecretResponseDto {
  @ApiProperty({
    description: 'MFA secret key',
    example: 'JBSWY3DPEHPK3PXP',
  })
  secret: string;

  @ApiProperty({
    description: 'QR code data URL',
    example: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...',
  })
  qrCode: string;

  @ApiProperty({
    description: 'Backup codes',
    example: ['ABC123', 'DEF456', 'GHI789'],
  })
  backupCodes: string[];
}

export class MFAStatusResponseDto {
  @ApiProperty({
    description: 'MFA enabled status',
    example: true,
  })
  enabled: boolean;

  @ApiProperty({
    description: 'MFA secret exists',
    example: true,
  })
  secretExists: boolean;
}

export class MFAResponseDto {
  @ApiProperty({
    description: 'Success status',
    example: true,
  })
  success: boolean;

  @ApiProperty({
    description: 'Response message',
    example: 'MFA enabled successfully',
  })
  message: string;

  @ApiProperty({
    description: 'Additional data',
    required: false,
  })
  @IsOptional()
  data?: any;
}
