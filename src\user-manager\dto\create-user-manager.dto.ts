import { ApiProperty } from '@nestjs/swagger';
import {
  IsEmail,
  IsString,
  IsOptional,
  IsEnum,
  IsBoolean,
} from 'class-validator';
import { Role } from 'src/common/enum/role.enum';

export class CreateUserManagerDto {
  @ApiProperty({ description: 'Email của user' })
  @IsEmail({}, { message: 'Email không hợp lệ' })
  email: string;

  @ApiProperty({ description: 'Firebase ID của user' })
  @IsString()
  firebaseId: string;

  @ApiProperty({ description: 'Role của user', enum: Role, default: Role.USER })
  @IsEnum(Role)
  @IsOptional()
  role?: Role = Role.USER;

  @ApiProperty({ description: 'Tên đầy đủ của user', required: false })
  @IsString()
  @IsOptional()
  fullName?: string;

  @ApiProperty({ description: 'Số điện thoại', required: false })
  @IsString()
  @IsOptional()
  phoneNumber?: string;

  @ApiProperty({ description: 'Avatar URL', required: false })
  @IsString()
  @IsOptional()
  avatarUrl?: string;
}

export class CreateUserWithSettingsDto extends CreateUserManagerDto {
  @ApiProperty({
    description: 'Theme setting',
    required: false,
    default: 'light',
  })
  @IsString()
  @IsOptional()
  theme?: string = 'light';

  @ApiProperty({
    description: 'Language setting',
    required: false,
    default: 'vi',
  })
  @IsString()
  @IsOptional()
  language?: string = 'vi';

  @ApiProperty({
    description: 'Notifications enabled',
    required: false,
    default: true,
  })
  @IsBoolean()
  @IsOptional()
  notificationsEnabled?: boolean = true;

  @ApiProperty({
    description: 'Email notifications enabled',
    required: false,
    default: true,
  })
  @IsBoolean()
  @IsOptional()
  emailNotifications?: boolean = true;

  @ApiProperty({
    description: 'Push notifications enabled',
    required: false,
    default: true,
  })
  @IsBoolean()
  @IsOptional()
  pushNotifications?: boolean = true;

  @ApiProperty({
    description: 'Auto save enabled',
    required: false,
    default: true,
  })
  @IsBoolean()
  @IsOptional()
  autoSave?: boolean = true;

  @ApiProperty({ description: 'Sound enabled', required: false, default: true })
  @IsBoolean()
  @IsOptional()
  soundEnabled?: boolean = true;

  @ApiProperty({
    description: 'Timezone',
    required: false,
    default: 'Asia/Ho_Chi_Minh',
  })
  @IsString()
  @IsOptional()
  timezone?: string = 'Asia/Ho_Chi_Minh';

  @ApiProperty({
    description: 'Date format',
    required: false,
    default: 'DD/MM/YYYY',
  })
  @IsString()
  @IsOptional()
  dateFormat?: string = 'DD/MM/YYYY';

  @ApiProperty({ description: 'Time format', required: false, default: '24' })
  @IsString()
  @IsOptional()
  timeFormat?: string = '24';
}
