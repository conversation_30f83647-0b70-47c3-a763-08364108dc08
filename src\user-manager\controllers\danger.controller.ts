import { Controller, Post, HttpCode } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { UserManagerService } from '../services/user-manager.service';

@ApiTags('Danger Zone')
@Controller('danger')
export class DangerController {
  constructor(private readonly userManagerService: UserManagerService) {}

  /**
   * Delete all users in both Firebase and the local database (no auth required)
   */
  @Post('delete-all-no-auth')
  @HttpCode(200)
  @ApiOperation({
    summary: 'Delete all users in both Firebase and DB (no auth required)',
  })
  @ApiResponse({
    status: 200,
    description: 'All users deleted from Firebase and DB',
  })
  async deleteAllNoAuth() {
    return this.userManagerService.deleteAllUsersEverywhere();
  }
}
