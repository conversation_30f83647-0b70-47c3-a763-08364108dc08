import {
  Controller,
  Get,
  Put,
  Post,
  Body,
  Param,
  UseGuards,
  HttpCode,
  HttpStatus,
  UseInterceptors,
  UploadedFile,
  Req,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiConsumes,
} from '@nestjs/swagger';
import { ProfileService } from '../services/profile.service';
import { FirebaseAuthGuard, RolesGuard } from '../../auth/guards/auth.guards';
import { Roles, CurrentUser } from '../../auth/decorators/auth.decorators';
import { Role } from '../../common/enum/role.enum';
import { ProfileResponseDto } from '../dto/ProfileResponse.dto';
import { UpdateAvatarDto } from '../dto/UpdateAvatar.dto';
import { UpdateProfileDto } from '../dto/UpdateProfile.dto';
import { AccountStatusResponseDto } from '../../common/dto';
import { ToggleAccountStatusDto } from '../dto/ToggleAccountStatus.dto';
import { DeactivateAccountDto } from '../dto/DeactivateAccount.dto';
import { ReactivateAccountDto } from '../dto/ReactivateAccount.dto';

@ApiTags('Profile Management')
@Controller('profile')
@UseGuards(FirebaseAuthGuard)
@ApiBearerAuth('JWT-auth')
export class ProfileController {
  constructor(private readonly profileService: ProfileService) {}

  @Get('me')
  @ApiOperation({
    summary: 'Get current user profile',
    description: 'Lấy thông tin profile của user hiện tại',
  })
  @ApiResponse({
    status: 200,
    description: 'Profile retrieved successfully',
    type: ProfileResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
  })
  async getCurrentUserProfile(
    @CurrentUser() user: any,
  ): Promise<ProfileResponseDto> {
    // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
    return this.profileService.getUserProfile(user.id);
  }

  @Get(':userId')
  @ApiOperation({
    summary: 'Get user profile by ID',
    description: 'Lấy thông tin profile của user theo ID (Admin only)',
  })
  @ApiParam({
    name: 'userId',
    description: 'User ID',
    example: 1,
  })
  @ApiResponse({
    status: 200,
    description: 'Profile retrieved successfully',
    type: ProfileResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - requires ADMIN role',
  })
  @ApiResponse({
    status: 404,
    description: 'User not found',
  })
  @UseGuards(RolesGuard)
  @Roles(Role.ADMIN)
  async getUserProfile(
    @Param('userId') userId: string,
  ): Promise<ProfileResponseDto> {
    return this.profileService.getUserProfile(userId);
  }

  @Put('avatar')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Update avatar',
    description: 'Cập nhật avatar cho user hiện tại',
  })
  @ApiResponse({
    status: 200,
    description: 'Avatar updated successfully',
    type: ProfileResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - invalid avatar URL',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - user account is inactive',
  })
  async updateAvatar(
    @CurrentUser() user: any,
    @Body() updateAvatarDto: UpdateAvatarDto,
  ): Promise<ProfileResponseDto> {
    return this.profileService.updateAvatar(user.id, updateAvatarDto);
  }

  @Post('avatar/upload')
  @HttpCode(HttpStatus.OK)
  @UseInterceptors(FileInterceptor('avatar'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({
    summary: 'Upload avatar file',
    description: 'Upload avatar file lên Firebase Storage cho user hiện tại',
  })
  @ApiResponse({
    status: 200,
    description: 'Avatar uploaded successfully',
    type: ProfileResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - invalid file or file too large',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - user account is inactive',
  })
  async uploadAvatarFile(
    @CurrentUser() user: any,
    @UploadedFile() avatar: Express.Multer.File,
  ): Promise<any> {
    return this.profileService.uploadAvatar(user.id, avatar);
  }

  @Put('me')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Update current user profile',
    description: 'Cập nhật profile của user hiện tại',
  })
  @ApiResponse({
    status: 200,
    description: 'Profile updated successfully',
    type: ProfileResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - invalid data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - user account is inactive',
  })
  async updateCurrentUserProfile(
    @CurrentUser() user: any,
    @Body() updateProfileDto: UpdateProfileDto,
    @Req() req,
  ): Promise<ProfileResponseDto> {
    return this.profileService.updateProfile(user.id, updateProfileDto);
  }

  @Put(':userId')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Update user profile by ID',
    description: 'Cập nhật profile của user theo ID (Admin only)',
  })
  @ApiParam({
    name: 'userId',
    description: 'User ID',
    example: 1,
  })
  @ApiResponse({
    status: 200,
    description: 'Profile updated successfully',
    type: ProfileResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - invalid data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - requires ADMIN role',
  })
  @ApiResponse({
    status: 404,
    description: 'User not found',
  })
  @UseGuards(RolesGuard)
  @Roles(Role.ADMIN)
  async updateUserProfile(
    @Param('userId') userId: string,
    @Body() updateProfileDto: UpdateProfileDto,
  ): Promise<ProfileResponseDto> {
    return this.profileService.updateProfile(userId, updateProfileDto);
  }

  @Post('toggle-status')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Toggle account status',
    description: 'Bật/tắt trạng thái active của account hiện tại',
  })
  @ApiResponse({
    status: 200,
    description: 'Account status updated successfully',
    type: AccountStatusResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
  })
  @ApiResponse({
    status: 403,
    description:
      'Forbidden - insufficient permissions or admin cannot deactivate self',
  })
  @ApiResponse({
    status: 404,
    description: 'User not found',
  })
  async toggleCurrentUserStatus(
    @CurrentUser() user: any,
    @Body() toggleAccountStatusDto: ToggleAccountStatusDto,
  ): Promise<AccountStatusResponseDto> {
    return this.profileService.toggleAccountStatus(
      user.id,
      toggleAccountStatusDto,
    );
  }

  @Post(':userId/toggle-status')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Toggle user account status by ID',
    description: 'Bật/tắt trạng thái active của user theo ID (Admin only)',
  })
  @ApiParam({
    name: 'userId',
    description: 'User ID',
    example: 1,
  })
  @ApiResponse({
    status: 200,
    description: 'Account status updated successfully',
    type: AccountStatusResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - requires ADMIN role or insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'User not found',
  })
  @UseGuards(RolesGuard)
  @Roles(Role.ADMIN)
  async toggleUserStatus(
    @Param('userId') userId: string,
    @CurrentUser() currentUser: any,
    @Body() toggleAccountStatusDto: ToggleAccountStatusDto,
  ): Promise<AccountStatusResponseDto> {
    return this.profileService.toggleAccountStatus(
      userId,
      toggleAccountStatusDto,
    );
  }

  @Post('deactivate')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Deactivate current user account',
    description: 'Deactivate account của user hiện tại',
  })
  @ApiResponse({
    status: 200,
    description: 'Account deactivated successfully',
    type: AccountStatusResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - confirmation required',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - admin cannot deactivate self',
  })
  @ApiResponse({
    status: 404,
    description: 'User not found',
  })
  async deactivateCurrentUser(
    @CurrentUser() user: any,
    @Body() deactivateAccountDto: DeactivateAccountDto,
  ): Promise<AccountStatusResponseDto> {
    return this.profileService.deactivateAccount(user.id, deactivateAccountDto);
  }

  @Post(':userId/deactivate')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Deactivate user account by ID',
    description: 'Deactivate account của user theo ID (Admin only)',
  })
  @ApiParam({
    name: 'userId',
    description: 'User ID',
    example: 1,
  })
  @ApiResponse({
    status: 200,
    description: 'Account deactivated successfully',
    type: AccountStatusResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - confirmation required',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
  })
  @ApiResponse({
    status: 403,
    description:
      'Forbidden - requires ADMIN role or admin cannot deactivate self',
  })
  @ApiResponse({
    status: 404,
    description: 'User not found',
  })
  @UseGuards(RolesGuard)
  @Roles(Role.ADMIN)
  async deactivateUser(
    @Param('userId') userId: string,
    @CurrentUser() currentUser: any,
    @Body() deactivateAccountDto: DeactivateAccountDto,
  ): Promise<AccountStatusResponseDto> {
    return this.profileService.deactivateAccount(userId, deactivateAccountDto);
  }

  @Post(':userId/reactivate')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Reactivate user account by ID',
    description: 'Reactivate account của user theo ID (Admin only)',
  })
  @ApiParam({
    name: 'userId',
    description: 'User ID',
    example: 1,
  })
  @ApiResponse({
    status: 200,
    description: 'Account reactivated successfully',
    type: AccountStatusResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - requires ADMIN role',
  })
  @ApiResponse({
    status: 404,
    description: 'User not found',
  })
  @UseGuards(RolesGuard)
  @Roles(Role.ADMIN)
  async reactivateUser(
    @Param('userId') userId: string,
    @CurrentUser() currentUser: any,
    @Body() reactivateAccountDto: ReactivateAccountDto,
  ): Promise<AccountStatusResponseDto> {
    return this.profileService.reactivateAccount(userId, reactivateAccountDto);
  }
}
