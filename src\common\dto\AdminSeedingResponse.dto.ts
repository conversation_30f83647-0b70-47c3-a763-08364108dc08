import { BaseResponseDto } from './BaseResponse.dto';
import { UserResponseDto } from './UserResponse.dto';
import { ProfileResponseDataDto } from './ProfileResponseData.dto';
import { SettingsResponseDto } from './SettingsResponse.dto';
import { FirebaseUserResponseDto } from './FirebaseUserResponse.dto';

export class AdminSeedingResponseDto extends BaseResponseDto<{
  user: UserResponseDto;
  profile: ProfileResponseDataDto;
  settings: SettingsResponseDto[];
  firebaseUser: FirebaseUserResponseDto;
}> {}
