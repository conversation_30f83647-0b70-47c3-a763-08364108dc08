import { Entity, Column } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { BaseEntity } from '../../common/base/base.entity';
// <PERSON><PERSON> thể giữ lại enum này nếu muốn validate phía code, nhưng không dùng cho DB nữa
export enum EmailType {
  WELCOME = 'WELCOME',
  PASSWORD_RESET = 'PASSWORD_RESET',
  VERIFICATION = 'VERIFICATION',
  NOTIFICATION = 'NOTIFICATION',
}

export enum EmailStatus {
  PENDING = 'PENDING',
  SENT = 'SENT',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED',
}

@Entity('email_queue')
export class EmailQueue extends BaseEntity {
  @Column({ type: 'varchar', length: 255 })
  @ApiProperty()
  to_email: string;

  @Column({ type: 'varchar', length: 255 })
  @ApiProperty()
  subject: string;

  @Column({ type: 'text' })
  @ApiProperty()
  content: string;

  @Column({
    type: 'varchar',
    length: 50,
    default: EmailType.WELCOME,
  })
  @ApiProperty({ enum: EmailType })
  email_type: string;

  @Column({
    type: 'varchar',
    length: 50,
    default: EmailStatus.PENDING,
  })
  @ApiProperty({ enum: EmailStatus })
  status: string;

  @Column({ type: 'json', nullable: true })
  @ApiProperty({ required: false })
  metadata?: Record<string, any>;

  @Column({ type: 'int', nullable: true })
  @ApiProperty({ required: false })
  retry_count?: number;

  @Column({ type: 'timestamp', nullable: true })
  @ApiProperty({ required: false })
  sent_at?: Date;

  @Column({ type: 'text', nullable: true })
  @ApiProperty({ required: false })
  error_message?: string;

  @Column({ type: 'timestamp', nullable: true })
  @ApiProperty({ required: false })
  scheduled_at?: Date;
}
