import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO that matches Java UserInfo model for Chat Service
 */
export class ChatUserInfoDto {
  @ApiProperty({
    description: 'User ID (UUID)',
    example: '123e4567-e89b-12d3-a456-************',
  })
  userId: string;

  @ApiProperty({
    description: 'User email',
    example: '<EMAIL>',
  })
  email: string;

  @ApiProperty({
    description: 'User display name',
    example: '<PERSON> Do<PERSON>',
  })
  displayName: string;

  @ApiProperty({
    description: 'User role',
    example: 'USER',
  })
  role: string;

  @ApiProperty({
    description: 'Email verification status',
    example: true,
  })
  emailVerified: boolean;

  @ApiProperty({
    description: 'User enabled status',
    example: true,
  })
  enabled: boolean;

  @ApiProperty({
    description: 'User avatar URL',
    example: 'https://example.com/avatar.jpg',
    required: false,
  })
  avatarUrl?: string;

  @ApiProperty({
    description: 'User phone number',
    example: '+1234567890',
    required: false,
  })
  phoneNumber?: string;

  @ApiProperty({
    description: 'User company',
    example: 'Bright Company',
    required: false,
  })
  company?: string;

  @ApiProperty({
    description: 'User occupation',
    example: 'Software Engineer',
    required: false,
  })
  occupation?: string;
}

/**
 * DTO that matches Java UserProfile model for Chat Service
 */
export class ChatUserProfileDto {
  @ApiProperty({
    description: 'User ID (UUID)',
    example: '123e4567-e89b-12d3-a456-************',
  })
  userId: string;

  @ApiProperty({
    description: 'User email',
    example: '<EMAIL>',
  })
  email: string;

  @ApiProperty({
    description: 'User display name',
    example: 'John Doe',
  })
  displayName: string;

  @ApiProperty({
    description: 'User role',
    example: 'USER',
  })
  role: string;

  @ApiProperty({
    description: 'Email verification status',
    example: true,
  })
  emailVerified: boolean;

  @ApiProperty({
    description: 'User enabled status',
    example: true,
  })
  enabled: boolean;
}

/**
 * Request DTO for batch user operations
 */
export class BatchUserRequestDto {
  @ApiProperty({
    description: 'List of user IDs',
    example: [
      '123e4567-e89b-12d3-a456-************',
      '456e7890-e89b-12d3-a456-************',
    ],
    type: [String],
  })
  userIds: string[];
}

/**
 * Request DTO for user search
 */
export class UserSearchRequestDto {
  @ApiProperty({
    description: 'Search query',
    example: 'john',
  })
  q: string;

  @ApiProperty({
    description: 'Maximum number of results',
    example: 10,
    required: false,
    default: 10,
  })
  limit?: number;
}
