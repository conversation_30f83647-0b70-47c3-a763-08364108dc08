import { Test, TestingModule } from '@nestjs/testing';
import { ChatApiController } from './chat-api.controller';
import { ChatApiService } from '../services/chat-api.service';
import { NotFoundException, BadRequestException } from '@nestjs/common';
import { ChatUserInfoDto, ChatUserProfileDto } from '../dto/chat-user-info.dto';

describe('ChatApiController', () => {
  let controller: ChatApiController;
  let service: ChatApiService;

  const mockChatApiService = {
    getUserById: jest.fn(),
    getUsersByIds: jest.fn(),
    searchUsers: jest.fn(),
    userExists: jest.fn(),
    validateUserIds: jest.fn(),
    getUserRole: jest.fn(),
    getUserProfile: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ChatApiController],
      providers: [
        {
          provide: ChatApiService,
          useValue: mockChatApiService,
        },
      ],
    }).compile();

    controller = module.get<ChatApiController>(ChatApiController);
    service = module.get<ChatApiService>(ChatApiService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getUserById', () => {
    it('should return user info when user exists', async () => {
      const userId = '123e4567-e89b-12d3-a456-426614174000';
      const mockUser: ChatUserInfoDto = {
        userId,
        email: '<EMAIL>',
        displayName: 'Test User',
        role: 'USER',
        emailVerified: true,
        enabled: true,
      };

      mockChatApiService.getUserById.mockResolvedValue(mockUser);

      const result = await controller.getUserById(userId);

      expect(service.getUserById).toHaveBeenCalledWith(userId);
      expect(result).toEqual(mockUser);
    });

    it('should throw NotFoundException when user does not exist', async () => {
      const userId = 'non-existent-id';
      mockChatApiService.getUserById.mockResolvedValue(null);

      await expect(controller.getUserById(userId)).rejects.toThrow(
        NotFoundException,
      );
      expect(service.getUserById).toHaveBeenCalledWith(userId);
    });
  });

  describe('getUsersByIds', () => {
    it('should return array of users', async () => {
      const userIds = ['id1', 'id2'];
      const mockUsers: ChatUserInfoDto[] = [
        {
          userId: 'id1',
          email: '<EMAIL>',
          displayName: 'User 1',
          role: 'USER',
          emailVerified: true,
          enabled: true,
        },
        {
          userId: 'id2',
          email: '<EMAIL>',
          displayName: 'User 2',
          role: 'ADMIN',
          emailVerified: true,
          enabled: true,
        },
      ];

      mockChatApiService.getUsersByIds.mockResolvedValue(mockUsers);

      const result = await controller.getUsersByIds({ userIds });

      expect(service.getUsersByIds).toHaveBeenCalledWith(userIds);
      expect(result).toEqual(mockUsers);
    });

    it('should throw BadRequestException when userIds array is empty', async () => {
      await expect(controller.getUsersByIds({ userIds: [] })).rejects.toThrow(
        BadRequestException,
      );
    });
  });

  describe('searchUsers', () => {
    it('should return search results', async () => {
      const query = 'john';
      const limit = 10;
      const mockResults: ChatUserInfoDto[] = [
        {
          userId: 'id1',
          email: '<EMAIL>',
          displayName: 'John Doe',
          role: 'USER',
          emailVerified: true,
          enabled: true,
        },
      ];

      mockChatApiService.searchUsers.mockResolvedValue(mockResults);

      const result = await controller.searchUsers(query, limit);

      expect(service.searchUsers).toHaveBeenCalledWith(query, limit);
      expect(result).toEqual(mockResults);
    });

    it('should throw BadRequestException when query is empty', async () => {
      await expect(controller.searchUsers('', 10)).rejects.toThrow(
        BadRequestException,
      );
      await expect(controller.searchUsers('   ', 10)).rejects.toThrow(
        BadRequestException,
      );
    });

    it('should use default limit when not provided', async () => {
      const query = 'john';
      mockChatApiService.searchUsers.mockResolvedValue([]);

      await controller.searchUsers(query);

      expect(service.searchUsers).toHaveBeenCalledWith(query, 10);
    });
  });

  describe('userExists', () => {
    it('should return true when user exists', async () => {
      const userId = 'existing-id';
      mockChatApiService.userExists.mockResolvedValue(true);

      const result = await controller.userExists(userId);

      expect(service.userExists).toHaveBeenCalledWith(userId);
      expect(result).toBe(true);
    });

    it('should return false when user does not exist', async () => {
      const userId = 'non-existent-id';
      mockChatApiService.userExists.mockResolvedValue(false);

      const result = await controller.userExists(userId);

      expect(service.userExists).toHaveBeenCalledWith(userId);
      expect(result).toBe(false);
    });
  });

  describe('validateUserIds', () => {
    it('should return valid user IDs', async () => {
      const userIds = ['id1', 'invalid-id', 'id2'];
      const validIds = ['id1', 'id2'];
      mockChatApiService.validateUserIds.mockResolvedValue(validIds);

      const result = await controller.validateUserIds({ userIds });

      expect(service.validateUserIds).toHaveBeenCalledWith(userIds);
      expect(result).toEqual(validIds);
    });

    it('should return empty array when no userIds provided', async () => {
      const result = await controller.validateUserIds({ userIds: [] });
      expect(result).toEqual([]);
    });
  });

  describe('getUserRole', () => {
    it('should return user role', async () => {
      const userId = 'user-id';
      const role = 'ADMIN';
      mockChatApiService.getUserRole.mockResolvedValue(role);

      const result = await controller.getUserRole(userId);

      expect(service.getUserRole).toHaveBeenCalledWith(userId);
      expect(result).toBe(role);
    });

    it('should throw NotFoundException when user not found', async () => {
      const userId = 'non-existent-id';
      mockChatApiService.getUserRole.mockResolvedValue(null);

      await expect(controller.getUserRole(userId)).rejects.toThrow(
        NotFoundException,
      );
    });
  });

  describe('getUserProfile', () => {
    it('should return user profile', async () => {
      const userId = 'user-id';
      const mockProfile: ChatUserProfileDto = {
        userId,
        email: '<EMAIL>',
        displayName: 'Test User',
        role: 'USER',
        emailVerified: true,
        enabled: true,
      };

      mockChatApiService.getUserProfile.mockResolvedValue(mockProfile);

      const result = await controller.getUserProfile(userId);

      expect(service.getUserProfile).toHaveBeenCalledWith(userId);
      expect(result).toEqual(mockProfile);
    });

    it('should throw NotFoundException when profile not found', async () => {
      const userId = 'non-existent-id';
      mockChatApiService.getUserProfile.mockResolvedValue(null);

      await expect(controller.getUserProfile(userId)).rejects.toThrow(
        NotFoundException,
      );
    });
  });
});
