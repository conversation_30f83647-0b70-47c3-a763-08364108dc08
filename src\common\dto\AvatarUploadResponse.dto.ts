import { ApiProperty } from '@nestjs/swagger';
import { BaseResponseDto } from './BaseResponse.dto';

export class AvatarUploadResponseDto extends BaseResponseDto<{
  avatarUrl: string;
  filename?: string;
  size?: number;
  mimetype?: string;
}> {
  @ApiProperty({
    description: 'Avatar URL',
    example: 'https://example.com/avatar.jpg',
  })
  avatarUrl: string;

  @ApiProperty({
    description: 'File name',
    example: 'avatar.jpg',
    required: false,
  })
  filename?: string;

  @ApiProperty({
    description: 'File size in bytes',
    example: 1024000,
    required: false,
  })
  size?: number;

  @ApiProperty({
    description: 'File MIME type',
    example: 'image/jpeg',
    required: false,
  })
  mimetype?: string;
}
