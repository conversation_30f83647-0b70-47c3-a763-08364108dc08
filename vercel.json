{"version": 2, "builds": [{"src": "src/main.ts", "use": "@vercel/node"}], "routes": [{"src": "/(.*)", "dest": "src/main.ts", "methods": ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"], "headers": {"Access-Control-Allow-Origin": "*", "Access-Control-Allow-Credentials": "true", "Access-Control-Allow-Methods": "GET,OPTIONS,PATCH,DELETE,POST,PUT", "Access-Control-Allow-Headers": "X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version"}}]}