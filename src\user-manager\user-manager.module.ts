import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserManagerService } from './services/user-manager.service';
import { UserManagerController } from './controllers/user-manager.controller';
import { ProfileController } from './controllers/profile.controller';
import { UserDtoController } from './controllers/user-dto.controller';
import { UserDtoService } from './services/user-dto.service';
import { User } from './entities/user.entity';
import { UserProfile } from './entities/user-profile.entity';
import { UserSettings } from './entities/user-settings.entity';
import { EmailQueue } from './entities/email-queue.entity';
import { FirebaseService } from './services/firebase.service';
import { UserSettingsService } from './services/user-settings.service';
import { EmailQueueService } from './services/email-queue.service';
import { PasswordService } from './services/password.service';
import { ProfileService } from './services/profile.service';
import { FileUploadService } from './services/file-upload.service';
import { RedisModule } from '../common/modules/redis.module';
import { MapperModule } from '../common/modules/mapper.module';
import { RabbitMQService } from '../common/services/rabbitmq.service';
import { RoleEntity } from './entities/role.entity';
import { Permission } from './entities/permission.entity';
import { AdminUserController } from './controllers/admin-user.controller';
import {
  AdminPermissionController,
  AdminRoleController,
} from './controllers/admin-role.controller';
import { CommonModule } from '../common/common.module';
import { DangerController } from './controllers/danger.controller';

@Module({
  imports: [
    CommonModule,
    TypeOrmModule.forFeature([
      User,
      UserProfile,
      UserSettings,
      EmailQueue,
      RoleEntity,
      Permission,
    ]),
    RedisModule,
    MapperModule,
  ],
  controllers: [
    UserManagerController,
    ProfileController,
    UserDtoController,
    AdminUserController,
    AdminRoleController,
    AdminPermissionController,
    DangerController, // Đăng ký controller mới
  ],
  providers: [
    UserManagerService,
    FirebaseService,
    UserSettingsService,
    EmailQueueService,
    PasswordService,
    ProfileService,
    FileUploadService,
    UserDtoService,
    RabbitMQService,
  ],
  exports: [
    UserManagerService,
    FirebaseService,
    UserSettingsService,
    EmailQueueService,
    PasswordService,
    ProfileService,
    FileUploadService,
    UserDtoService,
  ],
})
export class UserManagerModule {}
