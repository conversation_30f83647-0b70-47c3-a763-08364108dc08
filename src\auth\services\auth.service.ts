import { Injectable, UnauthorizedException, Logger } from '@nestjs/common';
import { FirebaseService } from '../../user-manager/services/firebase.service';
import { UserManagerService } from '../../user-manager/services/user-manager.service';
import { MFAService } from './mfa.service';
import { MapperService } from '../../common/services/mapper.service';
import { VerifyTokenDto } from '../dto/auth.dto';
import { Role } from '../../common/enum/role.enum';
import { AuditLogService } from '../../common/services/audit-log.service';

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);

  constructor(
    private firebaseService: FirebaseService,
    private userManagerService: UserManagerService,
    private mfaService: MFAService,
    private mapperService: MapperService,
    private auditLogService: AuditLogService,
  ) {}

  /**
   * Verify Firebase token và trả về thông tin user (optimized)
   */
  async verifyToken(verifyTokenDto: VerifyTokenDto) {
    try {
      // Log thông tin server và database
      this.logger.log(`NODE_ENV: ${process.env.NODE_ENV}`);
      this.logger.log(`DB_HOST: ${process.env.DB_HOST}`);
      this.logger.log(`DB_NAME: ${process.env.DB_NAME}`);
      this.logger.log(`DB_USER: ${process.env.DB_USERNAME}`);
      this.logger.log(`DB_PORT: ${process.env.DB_PORT}`);
      this.logger.log(`VITE_ENV: ${process.env.VITE_ENV}`);
      const { token } = verifyTokenDto;

      // Verify Firebase token
      const decodedToken = await this.firebaseService.verifyIdToken(token);

      // Get user from database
      const user = await this.userManagerService.findByFirebaseId(
        decodedToken.uid,
      );

      if (!user || !user.is_active) {
        throw new UnauthorizedException('User not found or inactive');
      }

      // Get user with all data in one query (optimized)
      const userWithData = await this.userManagerService.getUserWithAllData(
        user.id,
      );

      // Ghi audit log
      await this.auditLogService.sendUserAuditLog({
        userId: user.id,
        action: 'VERIFY_TOKEN',
        meta: { email: user.email },
      });

      this.logger.log(`User ${user.email} verified successfully`);
      console.log(userWithData.profile);
      return this.mapperService.createAuthVerifyResponse(
        userWithData,
        userWithData.profile,
        userWithData.settings,
        {
          uid: decodedToken.uid,
          email: decodedToken.email,
          email_verified: decodedToken.email_verified,
          name: decodedToken.name,
          picture: decodedToken.picture,
        },
        token,
      );
    } catch (error) {
      this.logger.error('Token verification failed:', error);
      throw new UnauthorizedException('Invalid token');
    }
  }

  /**
   * Verify Firebase token với MFA (nếu enabled)
   */
  async verifyTokenWithMFA(verifyTokenDto: VerifyTokenDto, mfaToken?: string) {
    try {
      const { token } = verifyTokenDto;

      // Verify Firebase token
      const decodedToken = await this.firebaseService.verifyIdToken(token);

      // Get user from database
      const user = await this.userManagerService.findByFirebaseId(
        decodedToken.uid,
      );

      if (!user || !user.is_active) {
        throw new UnauthorizedException('User not found or inactive');
      }

      // Check if MFA is enabled
      if (user.useMfaSecret && user.mfaSecret) {
        if (!mfaToken) {
          throw new UnauthorizedException('MFA token required');
        }

        // Verify MFA token
        const isMFAValid = await this.mfaService.verifyMFA(user.id, mfaToken);
        if (!isMFAValid) {
          throw new UnauthorizedException('Invalid MFA token');
        }
      }

      // Get user profile
      const userProfile = await this.userManagerService.getUserWithAllData(
        user.id,
      );

      // Get user settings
      const userSettings = await this.userManagerService.getUserSettings(
        user.id,
      );

      this.logger.log(`User ${user.email} verified successfully with MFA`);

      return {
        success: true,
        user: {
          id: user.id,
          email: user.email,
          firebaseId: user.firebaseId,
          role: user.role,
          is_active: user.is_active,
          mfa_enabled: user.useMfaSecret || false,
        },
        profile: userProfile.profile,
        settings: userSettings,
        firebase: {
          uid: decodedToken.uid,
          email: decodedToken.email,
          email_verified: decodedToken.email_verified,
          name: decodedToken.name,
          picture: decodedToken.picture,
        },
        token: token, // Return the original Firebase token
        message: 'Token verified successfully with MFA',
      };
    } catch (error) {
      this.logger.error('Token verification with MFA failed:', error);
      throw error;
    }
  }

  /**
   * Refresh Firebase token using refresh token
   */
  async refreshToken(refreshToken: string) {
    try {
      this.logger.log('Attempting to refresh Firebase token...');

      // Get Firebase Web API Key
      const webApiKey = process.env.FIREBASE_WEB_API_KEY;
      if (!webApiKey) {
        this.logger.error('FIREBASE_WEB_API_KEY is not configured');
        throw new UnauthorizedException('Firebase configuration is incomplete');
      }

      const response = await fetch(
        `https://securetoken.googleapis.com/v1/token?key=${webApiKey}`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            grant_type: 'refresh_token',
            refresh_token: refreshToken,
          }),
        },
      );

      const data = await response.json();

      if (!response.ok) {
        this.logger.error(`Token refresh failed: ${data.error?.message}`);
        throw new UnauthorizedException('Invalid refresh token');
      }

      const { id_token, refresh_token, expires_in } = data;

      // Verify the new token
      const decodedToken = await this.firebaseService.verifyIdToken(id_token);

      // Get user from database
      const user = await this.userManagerService.findByFirebaseId(
        decodedToken.uid,
      );

      if (!user || !user.is_active) {
        throw new UnauthorizedException('User not found or inactive');
      }

      this.logger.log(
        `Firebase token refreshed successfully for user: ${user.email}`,
      );

      return {
        success: true,
        token: id_token,
        refreshToken: refresh_token,
        expiresIn: parseInt(expires_in),
        tokenType: 'Bearer',
        user: {
          id: user.id,
          email: user.email,
          role: user.role,
          is_active: user.is_active,
        },
        message: 'Token refreshed successfully',
      };
    } catch (error) {
      this.logger.error('Error refreshing Firebase token:', error);
      throw new UnauthorizedException('Failed to refresh token');
    }
  }

  /**
   * Logout user
   */
  async logout(userId: string) {
    try {
      // Update last login time
      const user = await this.userManagerService.findOne(userId);
      if (user) {
        // You can add additional logout logic here
        // For example, invalidate tokens, log activity, etc.
        this.logger.log(`User ${user.email} logged out`);
      }

      return {
        success: true,
        message: 'Logged out successfully',
      };
    } catch (error) {
      this.logger.error('Logout failed:', error);
      throw new UnauthorizedException('Logout failed');
    }
  }

  /**
   * Get current user profile (optimized)
   */
  async getCurrentUser(userId: string) {
    try {
      const userWithData =
        await this.userManagerService.getUserWithAllData(userId);

      return {
        success: true,
        ...this.mapperService.mapUserWithProfileAndSettingsToDto(
          userWithData,
          userWithData.profile,
          userWithData.settings,
        ),
        message: 'User profile retrieved successfully',
      };
    } catch (error) {
      this.logger.error('Get current user failed:', error);
      throw new UnauthorizedException('Failed to get user profile');
    }
  }

  /**
   * Update user profile
   */
  async updateProfile(userId: string, profileData: any) {
    try {
      // Update user profile logic here
      // This would depend on your UserManagerService implementation

      return {
        success: true,
        message: 'Profile updated successfully',
      };
    } catch (error) {
      this.logger.error('Update profile failed:', error);
      throw new UnauthorizedException('Failed to update profile');
    }
  }

  /**
   * Check if user has specific role
   */
  async hasRole(userId: string, requiredRole: string): Promise<boolean> {
    try {
      const user = await this.userManagerService.findOne(userId);
      return user && user.role?.name === requiredRole;
    } catch (error) {
      return false;
    }
  }

  /**
   * Get user permissions based on role
   */
  async getUserPermissions(userId: string) {
    try {
      const user = await this.userManagerService.findOne(userId);

      if (!user) {
        throw new UnauthorizedException('User not found');
      }

      // Define permissions based on role
      const permissions = {
        [user.role?.name]: {
          can_read_users: true,
          can_create_users: user.role?.name === Role.ADMIN,
          can_update_users: user.role?.name === Role.ADMIN,
          can_delete_users: user.role?.name === Role.ADMIN,
          can_manage_settings: user.role?.name === Role.ADMIN,
          can_view_reports:
            user.role?.name === Role.ADMIN ||
            user.role?.name === Role.MODERATOR,
          can_manage_roles: user.role?.name === Role.ADMIN,
        },
      };

      return {
        success: true,
        role: user.role?.name,
        permissions: permissions[user.role?.name] || {},
        message: 'Permissions retrieved successfully',
      };
    } catch (error) {
      this.logger.error('Get user permissions failed:', error);
      throw new UnauthorizedException('Failed to get permissions');
    }
  }

  /**
   * Check if Firebase token is valid and get token info
   */
  async checkToken(token: string) {
    try {
      const decodedToken = await this.firebaseService.verifyIdToken(token);
      return decodedToken;
    } catch (error) {
      this.logger.error('Token check failed:', error);
      throw new UnauthorizedException('Invalid or expired token');
    }
  }
}
