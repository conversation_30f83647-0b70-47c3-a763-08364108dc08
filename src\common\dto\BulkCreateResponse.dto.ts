import { ApiProperty } from '@nestjs/swagger';
import { BaseResponseDto } from './BaseResponse.dto';

export class BulkCreateResponseDto extends BaseResponseDto<{
  total: number;
  successful: number;
  failed: number;
  results: any[];
  errors: any[];
}> {
  @ApiProperty({ description: 'Total number of users to create', example: 10 })
  total: number;

  @ApiProperty({
    description: 'Number of successfully created users',
    example: 8,
  })
  successful: number;

  @ApiProperty({ description: 'Number of failed creations', example: 2 })
  failed: number;

  @ApiProperty({ description: 'Successful results', type: [Object] })
  results: any[];

  @ApiProperty({ description: 'Error details', type: [Object] })
  errors: any[];
}
