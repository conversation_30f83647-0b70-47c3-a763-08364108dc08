import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  Injectable,
} from '@nestjs/common';
import { Request, Response } from 'express';
import { RabbitMQService } from './rabbitmq.service';
import {
  RABBITMQ_EXCHANGES,
  RABBITMQ_ROUTING_KEYS,
} from '../enum/rabbitmq.enum';

@Injectable()
@Catch()
export class GlobalExceptionFilter implements ExceptionFilter {
  constructor(private readonly rabbitMQService: RabbitMQService) {}

  async catch(exception: any, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const request = ctx.getRequest<Request>();
    const response = ctx.getResponse<Response>();
    console.log(exception);
    const status =
      exception instanceof HttpException ? exception.getStatus() : 500;
    const message = exception.message || 'Internal server error';
    const stack = exception.stack || null;

    // Lấy thông tin user nếu có
    const user = (request as any).user || null;

    // Lấy context nếu có
    const context = (request as any).context || {};
    const ip = context.ip || null;
    const device = context.device || null;
    const timezone = context.timezone || null;
    const platform = context.platform || null;
    const location = context.location || null;
    const lang = context.lang || null;

    // Định dạng log
    const log = {
      timestamp: new Date().toISOString(),
      service: process.env.SERVICE_NAME || 'bright-user',
      level: 'error',
      message,
      stack,
      status,
      path: request.originalUrl,
      method: request.method,
      userId: user?.id || null,
      ip,
      device,
      timezone,
      platform,
      location,
      lang,
      meta: {
        body: request.body,
        query: request.query,
        params: request.params,
      },
    };

    // Đẩy log ra RabbitMQ (không chặn response nếu lỗi)
    try {
      await this.rabbitMQService.publish(
        RABBITMQ_EXCHANGES.BRIGHT_TOPIC,
        RABBITMQ_ROUTING_KEYS.LOG_ERROR,
        log,
      );
    } catch (e) {
      // Nếu RabbitMQ lỗi thì vẫn trả response bình thường
      // Có thể log ra console nếu cần
      console.error(e);
    }

    // Trả response cho client
    response.status(status).json({
      statusCode: status,
      message,
      error: exception.name || 'Error',
    });
  }
}
