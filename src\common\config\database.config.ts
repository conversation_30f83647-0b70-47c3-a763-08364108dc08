import { registerAs } from '@nestjs/config';
import { join } from 'path';

export default registerAs('database', () => ({
  type: 'postgres',
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '5432', 10),
  username: process.env.DB_USERNAME || 'postgres',
  password: process.env.DB_PASSWORD || 'password',
  database: process.env.DB_NAME || 'bright_db',
  synchronize: process.env.NODE_ENV !== 'production', // Chỉ sync trong development
  logging: process.env.NODE_ENV !== 'production',
  entities: [join(__dirname, '..', '**', '*.entity.{ts,js}')],
  migrationsRun: true,
  ssl: { rejectUnauthorized: false }, // Bật SSL cho cloud database
}));
