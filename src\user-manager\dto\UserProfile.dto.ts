import { ApiProperty } from '@nestjs/swagger';

export class UserProfileDto {
  @ApiProperty({ description: 'User full name', example: '<PERSON>' })
  full_name: string;
  @ApiProperty({
    description: 'Phone number',
    example: '+1234567890',
    required: false,
  })
  phone_number?: string;
  @ApiProperty({
    description: 'Company name',
    example: 'Bright Company',
    required: false,
  })
  company?: string;
  @ApiProperty({
    description: 'Job title',
    example: 'Software Engineer',
    required: false,
  })
  occupation?: string;
  @ApiProperty({
    description: 'User bio',
    example: 'I am a software engineer...',
    required: false,
  })
  bio?: string;
  @ApiProperty({
    description: 'Address line 1',
    example: '123 Main St',
    required: false,
  })
  address1?: string;
  @ApiProperty({
    description: 'Address line 2',
    example: 'Apt 4B',
    required: false,
  })
  address2?: string;
  @ApiProperty({ description: 'City', example: 'New York', required: false })
  city?: string;
  @ApiProperty({
    description: 'State/Province',
    example: 'NY',
    required: false,
  })
  state?: string;
  @ApiProperty({
    description: 'ZIP/Postal code',
    example: '10001',
    required: false,
  })
  zipCode?: string;
  @ApiProperty({ description: 'Country', example: 'USA', required: false })
  country?: string;
  @ApiProperty({
    description: 'Date Of Birth',
    example: '11/11/2011',
    required: false,
  })
  dateOfBirth?: Date;
  @ApiProperty({
    description: 'Gender',
    example: 'male',
    enum: ['male', 'female', 'other'],
    required: false,
  })
  gender?: string;
}
