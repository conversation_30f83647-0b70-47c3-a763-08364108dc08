import { Injectable, Logger, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as admin from 'firebase-admin';
import { RabbitMQService } from '../../common/services/rabbitmq.service';
import {
  RABBITMQ_EXCHANGES,
  RABBITMQ_ROUTING_KEYS,
} from '../../common/enum/rabbitmq.enum';

export interface TokenResponse {
  token: string;
  expiresIn: number;
  refreshToken?: string;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

@Injectable()
export class TokenService {
  private readonly logger = new Logger(TokenService.name);

  constructor(
    private configService: ConfigService,
    private readonly rabbitMQService: RabbitMQService,
  ) {}

  /**
   * Helper: Send user audit log to RabbitMQ
   */
  private async sendUserAuditLog({
    userId,
    action,
    ip,
    device,
    meta,
  }: {
    userId: string;
    action: string;
    ip?: string;
    device?: string;
    meta?: any;
  }) {
    try {
      await this.rabbitMQService.publish(
        RABBITMQ_EXCHANGES.BRIGHT_TOPIC,
        RABBITMQ_ROUTING_KEYS.AUDIT_USER,
        {
          userId,
          action,
          timestamp: new Date().toISOString(),
          ip: ip || null,
          device: device || null,
          meta: meta || null,
        },
      );
    } catch (error) {
      this.logger.error('Error sending user audit log:', error);
    }
  }

  /**
   * Generate Firebase token for user by email and password
   */
  async generateTokenByCredentials(
    credentials: LoginCredentials,
    ip?: string,
    device?: string,
  ): Promise<TokenResponse> {
    try {
      this.logger.log(`Generating token for user: ${credentials.email}`);

      // Get Firebase Auth instance
      const auth = admin.auth();

      // Get Firebase Web API Key
      const webApiKey = process.env.FIREBASE_WEB_API_KEY;
      if (!webApiKey) {
        this.logger.error('FIREBASE_WEB_API_KEY is not configured');
        throw new UnauthorizedException('Firebase configuration is incomplete');
      }

      // Sign in with email and password using Firebase Auth REST API
      const response = await fetch(
        `https://identitytoolkit.googleapis.com/v1/accounts:signInWithPassword?key=${webApiKey}`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            email: credentials.email,
            password: credentials.password,
            returnSecureToken: true,
          }),
        },
      );

      const data = await response.json();

      if (!response.ok) {
        this.logger.error(
          `Firebase authentication failed: ${data.error?.message}`,
        );
        throw new UnauthorizedException('Invalid credentials');
      }

      const { idToken, refreshToken, expiresIn, localId } = data;

      this.logger.log(
        `Token generated successfully for user: ${credentials.email}`,
      );

      // Gửi audit log qua RabbitMQ
      await this.sendUserAuditLog({
        userId: localId || credentials.email,
        action: 'LOGIN',
        ip,
        device,
        meta: null,
      });

      return {
        token: idToken,
        expiresIn: parseInt(expiresIn),
        refreshToken,
      };
    } catch (error) {
      this.logger.error('Error generating token:', error);
      throw error;
    }
  }

  /**
   * Generate Firebase token for user by Firebase UID
   */
  async generateTokenByUid(uid: string): Promise<TokenResponse> {
    try {
      this.logger.log(`Generating custom token for user: ${uid}`);

      // Get Firebase Auth instance
      const auth = admin.auth();

      // Create custom token
      const customToken = await auth.createCustomToken(uid, {
        role: 'admin', // You can add custom claims here
      });

      this.logger.log(`Custom token generated successfully for user: ${uid}`);

      return {
        token: customToken,
        expiresIn: 3600, // Custom tokens expire in 1 hour
      };
    } catch (error) {
      this.logger.error('Error generating custom token:', error);
      throw error;
    }
  }

  /**
   * Refresh Firebase token
   */
  async refreshToken(refreshToken: string): Promise<TokenResponse> {
    try {
      this.logger.log('Refreshing Firebase token');

      // Get Firebase Web API Key
      const webApiKey = this.configService.get<string>('FIREBASE_WEB_API_KEY');
      if (!webApiKey) {
        this.logger.error('FIREBASE_WEB_API_KEY is not configured');
        throw new UnauthorizedException('Firebase configuration is incomplete');
      }

      const response = await fetch(
        `https://securetoken.googleapis.com/v1/token?key=${webApiKey}`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            grant_type: 'refresh_token',
            refresh_token: refreshToken,
          }),
        },
      );

      const data = await response.json();

      if (!response.ok) {
        this.logger.error(`Token refresh failed: ${data.error?.message}`);
        throw new UnauthorizedException('Invalid refresh token');
      }

      const { id_token, refresh_token, expires_in } = data;

      this.logger.log('Token refreshed successfully');

      return {
        token: id_token,
        expiresIn: parseInt(expires_in),
        refreshToken: refresh_token,
      };
    } catch (error) {
      this.logger.error('Error refreshing token:', error);
      throw error;
    }
  }

  /**
   * Verify Firebase token
   */
  async verifyToken(token: string): Promise<admin.auth.DecodedIdToken> {
    try {
      this.logger.log('Verifying Firebase token');

      const auth = admin.auth();
      const decodedToken = await auth.verifyIdToken(token);

      this.logger.log(
        `Token verified successfully for user: ${decodedToken.uid}`,
      );

      return decodedToken;
    } catch (error) {
      this.logger.error('Error verifying token:', error);
      throw new UnauthorizedException('Invalid token');
    }
  }

  /**
   * Get user info from token
   */
  async getUserFromToken(token: string): Promise<admin.auth.UserRecord> {
    try {
      this.logger.log('Getting user info from token');

      const decodedToken = await this.verifyToken(token);
      const auth = admin.auth();
      const userRecord = await auth.getUser(decodedToken.uid);

      this.logger.log(`User info retrieved successfully: ${userRecord.uid}`);

      return userRecord;
    } catch (error) {
      this.logger.error('Error getting user from token:', error);
      throw error;
    }
  }

  /**
   * Revoke refresh tokens for user
   */
  async revokeRefreshTokens(uid: string): Promise<void> {
    try {
      this.logger.log(`Revoking refresh tokens for user: ${uid}`);

      const auth = admin.auth();
      await auth.revokeRefreshTokens(uid);

      this.logger.log(`Refresh tokens revoked successfully for user: ${uid}`);
    } catch (error) {
      this.logger.error('Error revoking refresh tokens:', error);
      throw error;
    }
  }
}
