import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '../entities/user.entity';
import { UserProfile } from '../entities/user-profile.entity';
import { UserSettings } from '../entities/user-settings.entity';
import { MapperService } from '../../common/services/mapper.service';
import { RedisService } from '../../common/services/redis.service';
import { UserDto } from '../dto/user.dto';
import { UserSettingsDto } from '../dto/user-settings.dto';
import { UserProfileDto } from '../dto/UserProfile.dto';

@Injectable()
export class UserDtoService {
  private readonly logger = new Logger(UserDtoService.name);

  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(UserProfile)
    private readonly userProfileRepository: Repository<UserProfile>,
    @InjectRepository(UserSettings)
    private readonly userSettingsRepository: Repository<UserSettings>,
    private readonly mapperService: MapperService,
    private readonly redisService: RedisService,
  ) {}

  /**
   * Get all users as DTOs (with 7-day cache)
   */
  async getAllUsersAsDtos(): Promise<UserDto[]> {
    try {
      // Try to get from cache first
      const cacheKey = 'all_users_dtos';
      const cachedUserDtos = await this.redisService.get<UserDto[]>(cacheKey);

      if (cachedUserDtos) {
        this.logger.log(`All users DTOs retrieved from cache`);
        return cachedUserDtos;
      }

      const users = await this.userRepository.find({
        relations: ['profile', 'settings'],
      });

      // Map entities to DTOs using MapperService
      const userDtos = this.mapperService.mapUsersToDto(users);

      // Cache the user DTOs for 7 days
      await this.redisService.set(cacheKey, userDtos, { ttl: 604800 }); // 7 days

      this.logger.log(
        `Retrieved ${userDtos.length} users as DTOs and cached for 7 days`,
      );
      return userDtos;
    } catch (error) {
      this.logger.error('Error getting users as DTOs:', error);
      throw error;
    }
  }

  /**
   * Get user by ID as DTO (with 7-day cache)
   */
  async getUserByIdAsDto(id: string): Promise<UserDto> {
    try {
      // Try to get from cache first
      const cacheKey = `user_dto:${id}`;
      const cachedUserDto = await this.redisService.get<UserDto>(cacheKey);

      if (cachedUserDto) {
        this.logger.log(`User DTO for ${id} retrieved from cache`);
        return cachedUserDto;
      }

      const user = await this.userRepository.findOne({
        where: { id },
        relations: ['profile', 'settings'],
      });

      if (!user) {
        throw new NotFoundException(`User with ID ${id} not found`);
      }

      // Map entity to DTO using MapperService
      const userDto = this.mapperService.mapUserToDto(user);

      // Cache the user DTO for 7 days
      await this.redisService.set(cacheKey, userDto, { ttl: 604800 }); // 7 days

      this.logger.log(`Retrieved user ${id} as DTO and cached for 7 days`);
      return userDto;
    } catch (error) {
      this.logger.error(`Error getting user ${id} as DTO:`, error);
      throw error;
    }
  }

  /**
   * Get user with profile as DTO
   */
  async getUserWithProfileAsDto(id: string) {
    try {
      const user = await this.userRepository.findOne({
        where: { id },
        relations: ['profile'],
      });

      if (!user) {
        throw new NotFoundException(`User with ID ${id} not found`);
      }

      // Map user with profile to DTO using MapperService
      const result = this.mapperService.mapUserWithProfileToDto(
        user,
        user.profile,
      );

      this.logger.log(`Retrieved user ${id} with profile as DTO`);
      return this.mapperService.createSuccessResponse(
        result,
        'User with profile retrieved successfully',
      );
    } catch (error) {
      this.logger.error(`Error getting user ${id} with profile as DTO:`, error);
      return this.mapperService.createErrorResponse(
        'Failed to get user with profile',
        error,
      );
    }
  }

  /**
   * Get user with profile and settings as DTO
   */
  async getUserWithProfileAndSettingsAsDto(id: string) {
    try {
      const user = await this.userRepository.findOne({
        where: { id },
        relations: ['profile', 'settings'],
      });

      if (!user) {
        throw new NotFoundException(`User with ID ${id} not found`);
      }

      // Map user with profile and settings to DTO using MapperService
      const result = this.mapperService.mapUserWithProfileAndSettingsToDto(
        user,
        user.profile,
        user.settings,
      );

      this.logger.log(`Retrieved user ${id} with profile and settings as DTO`);
      return this.mapperService.createSuccessResponse(
        result,
        'User with profile and settings retrieved successfully',
      );
    } catch (error) {
      this.logger.error(
        `Error getting user ${id} with profile and settings as DTO:`,
        error,
      );
      return this.mapperService.createErrorResponse(
        'Failed to get user with profile and settings',
        error,
      );
    }
  }

  /**
   * Get user settings as object
   */
  async getUserSettingsAsObject(id: string) {
    try {
      const user = await this.userRepository.findOne({
        where: { id },
        relations: ['settings'],
      });

      if (!user) {
        throw new NotFoundException(`User with ID ${id} not found`);
      }

      // Map settings to object format using MapperService
      const settingsObject = this.mapperService.mapUserSettingsToObject(
        user.settings,
      );

      this.logger.log(`Retrieved user ${id} settings as object`);
      return this.mapperService.createSuccessResponse(
        settingsObject,
        'User settings retrieved successfully',
      );
    } catch (error) {
      this.logger.error(`Error getting user ${id} settings as object:`, error);
      return this.mapperService.createErrorResponse(
        'Failed to get user settings',
        error,
      );
    }
  }

  /**
   * Get user settings as DTOs
   */
  async getUserSettingsAsDtos(id: string): Promise<UserSettingsDto[]> {
    try {
      const user = await this.userRepository.findOne({
        where: { id },
        relations: ['settings'],
      });

      if (!user) {
        throw new NotFoundException(`User with ID ${id} not found`);
      }

      // Map settings to DTOs using MapperService
      const settingsDtos = this.mapperService.mapUserSettingsToDto(
        user.settings,
      );

      this.logger.log(`Retrieved user ${id} settings as DTOs`);
      return settingsDtos;
    } catch (error) {
      this.logger.error(`Error getting user ${id} settings as DTOs:`, error);
      throw error;
    }
  }

  /**
   * Get user profile as DTO
   */
  async getUserProfileAsDto(id: string): Promise<UserProfileDto> {
    try {
      const user = await this.userRepository.findOne({
        where: { id },
        relations: ['profile'],
      });

      if (!user || !user.profile) {
        throw new NotFoundException(`User profile with ID ${id} not found`);
      }

      // Map profile to DTO using MapperService
      const profileDto = this.mapperService.mapUserProfileToDto(user.profile);

      this.logger.log(`Retrieved user ${id} profile as DTO`);
      return profileDto;
    } catch (error) {
      this.logger.error(`Error getting user ${id} profile as DTO:`, error);
      throw error;
    }
  }
}
