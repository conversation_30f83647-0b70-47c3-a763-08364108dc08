import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UserSettings } from '../entities/user-settings.entity';
import { User } from '../entities/user.entity';
import { RedisService } from '../../common/services/redis.service';

export interface DefaultUserSettings {
  theme: string;
  language: string;
  notifications_enabled: boolean;
  email_notifications: boolean;
  push_notifications: boolean;
  auto_save: boolean;
  sound_enabled: boolean;
  timezone: string;
  date_format: string;
  time_format: string;
  privacy_level: string;
  email_frequency: string;
  dashboard_layout: string;
  sidebar_collapsed: boolean;
  compact_mode: boolean;
}

@Injectable()
export class UserSettingsService {
  private readonly logger = new Logger(UserSettingsService.name);

  // Default settings configuration
  private readonly defaultSettings: DefaultUserSettings = {
    theme: 'light',
    language: 'vi',
    notifications_enabled: true,
    email_notifications: true,
    push_notifications: true,
    auto_save: true,
    sound_enabled: true,
    timezone: 'Asia/Ho_Chi_Minh',
    date_format: 'DD/MM/YYYY',
    time_format: '24',
    privacy_level: 'public',
    email_frequency: 'daily',
    dashboard_layout: 'default',
    sidebar_collapsed: false,
    compact_mode: false,
  };

  constructor(
    @InjectRepository(UserSettings)
    private userSettingsRepository: Repository<UserSettings>,
    private redisService: RedisService,
  ) {}

  /**
   * Create default settings for a new user
   */
  async createDefaultSettings(
    user: User,
    settings?: Array<{
      key: string;
      value: string;
      description?: string;
      datatype?: string;
    }>,
  ): Promise<UserSettings[]> {
    const defaultSettings = settings ?? [
      {
        key: 'theme',
        value: 'light',
        description: 'Giao diện sáng/tối',
        datatype: 'string',
      },
      {
        key: 'language',
        value: 'vi',
        description: 'Ngôn ngữ giao diện',
        datatype: 'string',
      },
      // Thêm các settings mặc định khác nếu cần
    ];

    const entities = defaultSettings.map((s) =>
      this.userSettingsRepository.create({
        user,
        setting_key: s.key,
        setting_value: s.value,
        description: s.description,
        datatype: s.datatype,
      }),
    );
    return this.userSettingsRepository.save(entities);
  }

  /**
   * Get user settings (with 7-day cache)
   */
  async getUserSettings(userId: string) {
    try {
      // Try to get from cache first
      const cacheKey = this.redisService.generateUserSettingsKey(userId);
      const cachedSettings =
        await this.redisService.get<UserSettings[]>(cacheKey);

      if (cachedSettings) {
        this.logger.log(`User settings for ${userId} retrieved from cache`);
        return cachedSettings;
      }

      const settings = await this.userSettingsRepository.find({
        where: { user: { id: userId } },
        // Removed relations: ['user'] to avoid duplicate data
      });

      // Cache the settings for 7 days
      await this.redisService.set(cacheKey, settings, { ttl: 604800 }); // 7 days

      this.logger.log(
        `User settings for ${userId} retrieved from database and cached for 7 days`,
      );
      return settings;
    } catch (error) {
      this.logger.error(
        `Error getting user settings for user ${userId}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Get user setting by key (with 7-day cache)
   */
  async getUserSetting(userId: string, settingKey: string) {
    try {
      // Try to get from cache first
      const cacheKey = `${this.redisService.generateUserSettingsKey(userId)}:${settingKey}`;
      const cachedSetting = await this.redisService.get<UserSettings>(cacheKey);

      if (cachedSetting) {
        this.logger.log(
          `User setting ${settingKey} for ${userId} retrieved from cache`,
        );
        return cachedSetting;
      }

      const setting = await this.userSettingsRepository.findOne({
        where: { user: { id: userId }, setting_key: settingKey },
        // Removed relations: ['user'] to avoid duplicate data
      });

      if (setting) {
        // Cache the setting for 7 days
        await this.redisService.set(cacheKey, setting, { ttl: 604800 }); // 7 days
        this.logger.log(
          `User setting ${settingKey} for ${userId} retrieved from database and cached for 7 days`,
        );
      }

      return setting;
    } catch (error) {
      this.logger.error(
        `Error getting user setting ${settingKey} for user ${userId}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Update a specific setting for a user
   */
  async updateUserSetting(
    userId: string,
    settingKey: string,
    settingValue: string,
  ): Promise<UserSettings> {
    try {
      let setting = await this.getUserSetting(userId.toString(), settingKey);

      if (!setting) {
        // Create new setting if it doesn't exist
        const user = { id: userId } as User;
        setting = this.userSettingsRepository.create({
          user,
          setting_key: settingKey,
          setting_value: settingValue,
        });
      } else {
        setting.setting_value = settingValue;
      }

      const savedSetting = await this.userSettingsRepository.save(setting);
      this.logger.log(`Updated setting ${settingKey} for user ${userId}`);

      return savedSetting;
    } catch (error) {
      this.logger.error('Error updating user setting:', error);
      throw new Error('Failed to update user setting');
    }
  }

  /**
   * Update multiple settings for a user
   */
  async updateMultipleSettings(
    userId: string,
    settings: Record<string, string>,
  ): Promise<UserSettings[]> {
    try {
      const updatedSettings: UserSettings[] = [];

      for (const [key, value] of Object.entries(settings)) {
        const setting = await this.updateUserSetting(userId, key, value);
        updatedSettings.push(setting);
      }

      this.logger.log(
        `Updated ${updatedSettings.length} settings for user ${userId}`,
      );
      return updatedSettings;
    } catch (error) {
      this.logger.error('Error updating multiple settings:', error);
      throw new Error('Failed to update multiple settings');
    }
  }

  /**
   * Delete a setting for a user
   */
  async deleteUserSetting(userId: string, settingKey: string): Promise<void> {
    try {
      const setting = await this.getUserSetting(userId.toString(), settingKey);
      if (setting) {
        await this.userSettingsRepository.remove(setting);
        this.logger.log(`Deleted setting ${settingKey} for user ${userId}`);
      }
    } catch (error) {
      this.logger.error('Error deleting user setting:', error);
      throw new Error('Failed to delete user setting');
    }
  }

  /**
   * Reset user settings to default
   */
  async resetToDefault(userId: string): Promise<UserSettings[]> {
    try {
      // Delete existing settings
      const existingSettings = await this.getUserSettings(userId.toString());
      if (existingSettings.length > 0) {
        await this.userSettingsRepository.remove(existingSettings);
      }

      // Create default settings
      const user = { id: userId } as User;
      const defaultSettings = await this.createDefaultSettings(user);

      this.logger.log(`Reset settings to default for user ${userId}`);
      return defaultSettings;
    } catch (error) {
      this.logger.error('Error resetting user settings:', error);
      throw new Error('Failed to reset user settings');
    }
  }

  /**
   * Get settings as key-value object (with 7-day cache)
   */
  async getSettingsAsObject(userId: string): Promise<Record<string, string>> {
    try {
      // Try to get from cache first
      const cacheKey = `${this.redisService.generateUserSettingsKey(userId)}:object`;
      const cachedSettingsObject =
        await this.redisService.get<Record<string, string>>(cacheKey);

      if (cachedSettingsObject) {
        this.logger.log(
          `User settings object for ${userId} retrieved from cache`,
        );
        return cachedSettingsObject;
      }

      const settings = await this.getUserSettings(userId.toString());
      const settingsObject: Record<string, string> = {};

      settings.forEach((setting) => {
        settingsObject[setting.setting_key] = setting.setting_value;
      });

      // Cache the settings object for 7 days
      await this.redisService.set(cacheKey, settingsObject, { ttl: 604800 }); // 7 days

      this.logger.log(
        `User settings object for ${userId} retrieved from database and cached for 7 days`,
      );
      return settingsObject;
    } catch (error) {
      this.logger.error('Error getting settings as object:', error);
      throw new Error('Failed to get settings as object');
    }
  }
}
