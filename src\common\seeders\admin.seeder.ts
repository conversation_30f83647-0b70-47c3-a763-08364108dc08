import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '../../user-manager/entities/user.entity';
import { UserProfile } from '../../user-manager/entities/user-profile.entity';
import { UserSettings } from '../../user-manager/entities/user-settings.entity';
import { FirebaseService } from '../../user-manager/services/firebase.service';
import { UserSettingsService } from '../../user-manager/services/user-settings.service';
import { MapperService } from '../services/mapper.service';
import { RoleEntity } from '../../user-manager/entities/role.entity';
import { RabbitMQService } from '../services/rabbitmq.service';
import {
  RABBITMQ_EXCHANGES,
  RABBITMQ_QUEUES,
  RABBITMQ_ROUTING_KEYS,
} from '../enum/rabbitmq.enum';

export interface AdminUserData {
  email: string;
  password: string;
  fullName: string;
  firebaseId?: string;
}

@Injectable()
export class AdminSeeder {
  private readonly logger = new Logger(AdminSeeder.name);

  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(UserProfile)
    private userProfileRepository: Repository<UserProfile>,
    @InjectRepository(UserSettings)
    private userSettingsRepository: Repository<UserSettings>,
    private firebaseService: FirebaseService,
    private userSettingsService: UserSettingsService,
    private mapperService: MapperService,
    private rabbitMQService: RabbitMQService,
  ) {}

  /**
   * Seed admin user
   */
  async seedAdmin(adminData: AdminUserData) {
    try {
      this.logger.log('Starting admin user seeding...');

      // Check if admin already exists
      const existingAdmin = await this.userRepository.findOne({
        where: { email: adminData.email },
      });

      if (existingAdmin) {
        this.logger.log(
          `Admin user with email ${adminData.email} already exists`,
        );
        return {
          success: true,
          message: 'Admin user already exists',
          user: existingAdmin,
        };
      }

      // Create Firebase user
      let firebaseUser;
      try {
        firebaseUser = await this.firebaseService.createUser({
          email: adminData.email,
          password: adminData.password,
          displayName: adminData.fullName,
        });
        this.logger.log(`Firebase user created: ${firebaseUser.uid}`);
      } catch (error) {
        this.logger.error('Failed to create Firebase user:', error);
        throw new Error(`Failed to create Firebase user: ${error.message}`);
      }

      // Create user in database
      const roleRepo = this.userRepository.manager.getRepository(RoleEntity);
      const adminRole = await roleRepo.findOne({ where: { name: 'admin' } });
      if (!adminRole) throw new Error('Admin role not found');
      const user = this.userRepository.create({
        email: adminData.email,
        firebaseId: firebaseUser.uid,
        role: adminRole,
        is_active: true,
      });
      const savedUser = await this.userRepository.save(user);
      this.logger.log(`Database user created: ${savedUser.id}`);

      // Create user profile
      const userProfile = this.userProfileRepository.create({
        user: savedUser,
        full_name: adminData.fullName,
        last_login: new Date(),
      });

      await this.userProfileRepository.save(userProfile);
      this.logger.log(`User profile created for admin`);

      // Create default settings
      const userSettings = await this.userSettingsService.createDefaultSettings(
        savedUser,
        [
          {
            key: 'theme',
            value: 'light',
            description: 'Giao diện sáng/tối',
            datatype: 'string',
          },
          {
            key: 'language',
            value: 'vi',
            description: 'Ngôn ngữ giao diện',
            datatype: 'string',
          },
          // Thêm các settings mặc định khác nếu cần
        ],
      );
      this.logger.log(`User settings created for admin`);

      // Set custom claims in Firebase
      try {
        await this.firebaseService.setCustomClaims(firebaseUser.uid, {
          role: 'admin',
          userId: savedUser.id,
          email: savedUser.email,
        });
        this.logger.log(`Firebase custom claims set for admin`);
      } catch (error) {
        this.logger.warn(
          `Failed to set Firebase custom claims: ${error.message}`,
        );
      }

      this.logger.log(`Admin user seeding completed successfully`);

      return this.mapperService.createAdminSeedingResponse(
        savedUser,
        userProfile,
        userSettings,
        firebaseUser,
      );
    } catch (error) {
      this.logger.error('Error seeding admin user:', error);
      throw error;
    }
  }

  /**
   * Seed default admin user
   */
  async seedDefaultAdmin() {
    const defaultAdminData: AdminUserData = {
      email: '<EMAIL>',
      password: 'Admin@123456',
      fullName: 'Bright Admin',
    };

    return this.seedAdmin(defaultAdminData);
  }

  /**
   * Get admin user by email
   */
  async getAdminByEmail(email: string) {
    try {
      // Lấy role admin
      const roleRepo = this.userRepository.manager.getRepository(RoleEntity);
      const adminRole = await roleRepo.findOne({ where: { name: 'admin' } });
      if (!adminRole) return null;
      const admin = await this.userRepository.findOne({
        where: { email, role: { id: adminRole.id } },
        relations: ['profile', 'settings'],
      });
      await this.bindQueueToExchange(
        RABBITMQ_QUEUES.AUDIT,
        RABBITMQ_EXCHANGES.BRIGHT_TOPIC,
        RABBITMQ_ROUTING_KEYS.AUDIT_USER,
      );
      await this.bindQueueToExchange(
        RABBITMQ_QUEUES.AUDIT,
        RABBITMQ_EXCHANGES.BRIGHT_TOPIC,
        RABBITMQ_ROUTING_KEYS.AUDIT_AUDIT,
      );
      await this.bindQueueToExchange(
        RABBITMQ_QUEUES.ERROR,
        RABBITMQ_EXCHANGES.BRIGHT_TOPIC,
        RABBITMQ_ROUTING_KEYS.LOG_ERROR,
      );
      await this.bindQueueToExchange(
        RABBITMQ_QUEUES.MAIL,
        RABBITMQ_EXCHANGES.BRIGHT_TOPIC,
        RABBITMQ_ROUTING_KEYS.MAIL_WELCOME,
      );
      await this.bindQueueToExchange(
        RABBITMQ_QUEUES.MAIL,
        RABBITMQ_EXCHANGES.BRIGHT_TOPIC,
        RABBITMQ_ROUTING_KEYS.MAIL_RESET,
      );
      return admin;
    } catch (error) {
      this.logger.error('Error getting admin by email:', error);
      throw error;
    }
  }

  /**
   * Get all admin users
   */
  async getAllAdmins() {
    try {
      // Lấy role admin
      const roleRepo = this.userRepository.manager.getRepository(RoleEntity);
      const adminRole = await roleRepo.findOne({ where: { name: 'admin' } });
      if (!adminRole) return [];
      const admins = await this.userRepository.find({
        where: { role: adminRole },
        relations: ['profile', 'settings'],
      });

      return admins;
    } catch (error) {
      this.logger.error('Error getting all admins:', error);
      throw error;
    }
  }

  async bindQueueToExchange(
    queue: string,
    exchange: string,
    routingKey: string,
  ) {
    await this.rabbitMQService.bindQueueToExchange(queue, exchange, routingKey);
  }
}
