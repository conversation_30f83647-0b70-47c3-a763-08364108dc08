import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsOptional,
  IsString,
  IsUrl,
  IsEnum,
  IsDateString,
} from 'class-validator';

export class UpdateProfileDto {
  @ApiProperty({
    description: 'Full name',
    example: '<PERSON> Do<PERSON>',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Full name must be a string' })
  fullName?: string;

  @ApiProperty({
    description: 'Phone number',
    example: '+1234567890',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Phone number must be a string' })
  phoneNumber?: string;

  @ApiProperty({
    description: 'Company name',
    example: 'Bright Company',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Company must be a string' })
  company?: string;

  @ApiProperty({
    description: 'Job title',
    example: 'Software Engineer',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Occupation must be a string' })
  occupation?: string;

  @ApiProperty({
    description: 'User bio',
    example: 'I am a software engineer...',
    required: false,
  })
  @IsOptional()
  @IsString({ message: '<PERSON><PERSON> must be a string' })
  bio?: string;

  @ApiProperty({
    description: 'Address line 1',
    example: '123 Main St',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Address1 must be a string' })
  address1?: string;

  @ApiProperty({
    description: 'Address line 2',
    example: 'Apt 4B',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Address2 must be a string' })
  address2?: string;

  @ApiProperty({ description: 'City', example: 'New York', required: false })
  @IsOptional()
  @IsString({ message: 'City must be a string' })
  city?: string;

  @ApiProperty({
    description: 'State/Province',
    example: 'NY',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'State must be a string' })
  state?: string;

  @ApiProperty({
    description: 'ZIP/Postal code',
    example: '10001',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'ZIP code must be a string' })
  zipCode?: string;

  @ApiProperty({ description: 'Country', example: 'USA', required: false })
  @IsOptional()
  @IsString({ message: 'Country must be a string' })
  country?: string;

  @ApiProperty({
    description: 'Gender',
    example: 'male',
    enum: ['male', 'female', 'other'],
    required: false,
  })
  @IsOptional()
  @IsEnum(['male', 'female', 'other'], {
    message: 'Gender must be male, female, or other',
  })
  gender?: string;

  @ApiProperty({
    description: 'Facebook URL',
    example: 'https://www.facebook.com/john.doe',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Facebook URL must be a string' })
  facebookUrl?: string;

  @ApiProperty({
    description: 'Twitter URL',
    example: 'https://www.twitter.com/john.doe',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Twitter URL must be a string' })
  twitterUrl?: string;

  @ApiProperty({
    description: 'Linkedin URL',
    example: 'https://www.linkedin.com/in/john.doe',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Linkedin URL must be a string' })
  linkedinUrl?: string;

  @ApiProperty({
    description: 'Date of birth',
    example: '2000-01-01',
    required: false,
  })
  @IsOptional()
  @Type(() => Date)
  dateOfBirth?: Date;
}
