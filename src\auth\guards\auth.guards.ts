import {
  Injectable,
  CanActivate,
  ExecutionContext,
  UnauthorizedException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Role } from '../../common/enum/role.enum';
import { ROLES_KEY, IS_PUBLIC_KEY } from '../decorators/auth.decorators';
import { FirebaseService } from '../../user-manager/services/firebase.service';
import { UserManagerService } from '../../user-manager/services/user-manager.service';
import { Request as ExpressRequest } from 'express';

// Đ<PERSON>nh nghĩa interface cho user trên request
interface RequestUser {
  id: string;
  email: string;
  firebaseId: string;
  role: string | { name: string };
  is_active: boolean;
  firebaseUid?: string;
  firebaseClaims?: any;
}

@Injectable()
export class FirebaseAuthGuard implements CanActivate {
  constructor(
    private firebaseService: FirebaseService,
    private userManagerService: UserManagerService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request: ExpressRequest = context.switchToHttp().getRequest();

    try {
      // Extract Firebase token from Authorization header
      const token = request.headers.authorization?.replace('Bearer ', '');
      if (!token) {
        throw new UnauthorizedException('No token provided');
      }

      // Verify Firebase token
      const decodedToken = await this.firebaseService.verifyIdToken(token);

      // Get user from database
      const user = await this.userManagerService.findByFirebaseId(
        decodedToken.uid,
      );

      if (!user || !user.is_active) {
        throw new UnauthorizedException('User not found or inactive');
      }

      // Attach user to request
      request.user = {
        id: user.id,
        email: user.email,
        firebaseId: user.firebaseId,
        role: user.role,
        is_active: user.is_active,
        firebaseUid: decodedToken.uid,
        firebaseClaims: decodedToken,
      };

      return true;
    } catch (error) {
      console.error('Firebase auth guard error:', error);
      throw new UnauthorizedException('Authentication failed');
    }
  }
}

@Injectable()
export class RolesGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const requiredRoles = this.reflector.getAllAndOverride<Role[]>(ROLES_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    if (!requiredRoles) {
      return true;
    }

    const request: ExpressRequest & { user?: RequestUser } = context
      .switchToHttp()
      .getRequest();
    if (!request.user) {
      // Nếu chưa xác thực, trả về lỗi 401
      throw new UnauthorizedException('User not authenticated');
    }
    return requiredRoles.some((role) => {
      const userRole = request.user?.role;
      if (typeof userRole === 'string') {
        return userRole === role;
      }
      if (
        typeof userRole === 'object' &&
        userRole !== null &&
        'name' in userRole
      ) {
        return (userRole as { name: string }).name === role;
      }
      return false;
    });
  }
}

@Injectable()
export class PublicGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    if (isPublic) {
      return true;
    }

    return true; // Continue with other guards
  }
}
