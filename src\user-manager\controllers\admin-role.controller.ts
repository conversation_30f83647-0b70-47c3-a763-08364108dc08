import {
  Controller,
  Get,
  Post,
  Patch,
  Delete,
  Param,
  Body,
  Query,
  UseGuards,
} from '@nestjs/common';
import { Roles } from '../../auth/decorators/auth.decorators';
import { Role } from '../../common/enum/role.enum';
import { RolesGuard } from '../../auth/guards/auth.guards';
import { UserManagerService } from '../services/user-manager.service';

@UseGuards(RolesGuard)
@Roles(Role.ADMIN)
@Controller('admin/roles')
export class AdminRoleController {
  constructor(private readonly userManagerService: UserManagerService) {}

  @Get()
  async listRoles() {
    return this.userManagerService.adminListRoles();
  }

  @Post()
  async createRole(@Body() dto) {
    return this.userManagerService.adminCreateRole(dto);
  }

  @Patch(':id')
  async updateRole(@Param('id') id: string, @Body() dto) {
    return this.userManagerService.adminUpdateRole(id, dto);
  }

  @Delete(':id')
  async deleteRole(@Param('id') id: string) {
    return this.userManagerService.adminDeleteRole(id);
  }

  @Get(':id/permissions')
  async getRolePermissions(@Param('id') id: string) {
    return this.userManagerService.adminGetRolePermissions(id);
  }

  @Post(':id/permissions')
  async addPermissionsToRole(
    @Param('id') id: string,
    @Body() dto: { permissionIds: string[] },
  ) {
    return this.userManagerService.adminAddPermissionsToRole(
      id,
      dto.permissionIds,
    );
  }

  @Delete(':id/permissions')
  async removePermissionsFromRole(
    @Param('id') id: string,
    @Body() dto: { permissionIds: string[] },
  ) {
    return this.userManagerService.adminRemovePermissionsFromRole(
      id,
      dto.permissionIds,
    );
  }
}

@UseGuards(RolesGuard)
@Roles(Role.ADMIN)
@Controller('admin/permissions')
export class AdminPermissionController {
  constructor(private readonly userManagerService: UserManagerService) {}

  @Get()
  async listPermissions() {
    return this.userManagerService.adminListPermissions();
  }

  @Post()
  async createPermission(@Body() dto) {
    return this.userManagerService.adminCreatePermission(dto);
  }

  @Patch(':id')
  async updatePermission(@Param('id') id: string, @Body() dto) {
    return this.userManagerService.adminUpdatePermission(id, dto);
  }

  @Delete(':id')
  async deletePermission(@Param('id') id: string) {
    return this.userManagerService.adminDeletePermission(id);
  }
}
