import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { AuthModule } from './auth/auth.module';
import { UserManagerModule } from './user-manager/user-manager.module';
import { RedisModule } from './common/modules/redis.module';
import databaseConfig from './common/config/database.config';
import firebaseConfig from './common/config/firebase.config';
import { GlobalExceptionFilter } from './common/services/global-exception.filter';
import { RabbitMQService } from './common/services/rabbitmq.service';
import { MiddlewareConsumer, NestModule } from '@nestjs/common';
import { RequestContext } from './common/context/request-context';
import { User } from './user-manager/entities/user.entity';
import { UserProfile } from './user-manager/entities/user-profile.entity';
import { UserSettings } from './user-manager/entities/user-settings.entity';
import { EmailQueue } from './user-manager/entities/email-queue.entity';
import { RoleEntity } from './user-manager/entities/role.entity';
import { Permission } from './user-manager/entities/permission.entity';

@Module({
  imports: [
    // Configuration Module
    ConfigModule.forRoot({
      isGlobal: true,
      load: [databaseConfig, firebaseConfig],
      envFilePath: '.env',
    }),

    // Database Module
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => {
        const dbConfig = configService.get('database');
        return {
          ...dbConfig,
          entities: [
            User,
            UserProfile,
            UserSettings,
            EmailQueue,
            RoleEntity,
            Permission,
          ],
        };
      },
      inject: [ConfigService],
    }),

    // Cache Module
    RedisModule,

    // Feature Modules
    AuthModule,
    UserManagerModule,
  ],
  controllers: [AppController],
  providers: [
    AppService,
    RabbitMQService,
    GlobalExceptionFilter,
    RequestContext,
  ],
})
export class AppModule implements NestModule {
  configure(_consumer: MiddlewareConsumer) {}
}
