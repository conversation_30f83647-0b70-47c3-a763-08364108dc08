import { ApiProperty } from '@nestjs/swagger';
import { BaseResponseDto } from './BaseResponse.dto';
import { UserResponseDto } from './UserResponse.dto';
import { ProfileResponseDataDto } from './ProfileResponseData.dto';
import { SettingsResponseDto } from './SettingsResponse.dto';
import { EmailQueueResponseDto } from './EmailQueueResponse.dto';

export class QuickCreateUserResponseDto extends BaseResponseDto<{
  user: UserResponseDto;
  profile: ProfileResponseDataDto;
  settings: SettingsResponseDto[];
}> {
  @ApiProperty({
    description: 'Email queue information',
    required: false,
  })
  emailQueue?: EmailQueueResponseDto;
}
