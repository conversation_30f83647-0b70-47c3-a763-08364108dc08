import { ApiProperty } from '@nestjs/swagger';

export class UserSettingsDto {
  @ApiProperty({
    description: 'Settings ID (UUID)',
    example: '123e4567-e89b-12d3-a456-************',
  })
  id: string;

  @ApiProperty({
    description: 'Setting key',
    example: 'theme',
  })
  key: string;

  @ApiProperty({
    description: 'Setting value',
    example: 'dark',
  })
  value: string;

  @ApiProperty({
    description: 'Setting description',
    example: 'User interface theme preference',
    required: false,
  })
  description?: string;

  @ApiProperty({
    description: 'Data type of the setting value',
    example: 'string',
    required: false,
  })
  datatype?: string;
}

export class CreateUserSettingsDto {
  @ApiProperty({
    description: 'Setting key',
    example: 'theme',
  })
  key: string;

  @ApiProperty({
    description: 'Setting value',
    example: 'dark',
  })
  value: string;

  @ApiProperty({
    description: 'Setting description',
    example: 'User interface theme preference',
    required: false,
  })
  description?: string;
}

export class UpdateUserSettingsDto {
  @ApiProperty({
    description: 'Setting value',
    example: 'dark',
  })
  value: string;

  @ApiProperty({
    description: 'Setting description',
    example: 'User interface theme preference',
    required: false,
  })
  description?: string;
}

export class UserSettingsResponseDto {
  @ApiProperty({
    description: 'Success status',
    example: true,
  })
  success: boolean;

  @ApiProperty({
    description: 'Response message',
    example: 'Settings retrieved successfully',
  })
  message: string;

  @ApiProperty({
    description: 'Settings data',
    type: [UserSettingsDto],
  })
  data: UserSettingsDto[];
}

export class UserSettingsObjectDto {
  @ApiProperty({
    description: 'Theme setting',
    example: 'dark',
    required: false,
  })
  theme?: string;

  @ApiProperty({
    description: 'Language setting',
    example: 'en',
    required: false,
  })
  language?: string;

  @ApiProperty({
    description: 'Notifications enabled',
    example: 'true',
    required: false,
  })
  notifications_enabled?: string;

  @ApiProperty({
    description: 'Email notifications',
    example: 'true',
    required: false,
  })
  email_notifications?: string;

  @ApiProperty({
    description: 'SMS notifications',
    example: 'false',
    required: false,
  })
  sms_notifications?: string;

  @ApiProperty({
    description: 'Privacy level',
    example: 'public',
    required: false,
  })
  privacy_level?: string;

  @ApiProperty({
    description: 'Timezone',
    example: 'UTC',
    required: false,
  })
  timezone?: string;

  @ApiProperty({
    description: 'Date format',
    example: 'MM/DD/YYYY',
    required: false,
  })
  date_format?: string;

  @ApiProperty({
    description: 'Time format',
    example: '12h',
    required: false,
  })
  time_format?: string;

  @ApiProperty({
    description: 'Currency',
    example: 'USD',
    required: false,
  })
  currency?: string;
}
