import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  HttpCode,
  HttpStatus,
  ValidationPipe,
  UsePipes,
  UseGuards,
  Req,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  Api<PERSON>earerAuth,
} from '@nestjs/swagger';
import { UserManagerService } from '../services/user-manager.service';
import { UserDtoService } from '../services/user-dto.service';
import { MapperService } from '../../common/services/mapper.service';
import {
  CreateUserDto,
  CreateUserWithSettingsDto,
  UpdateUserDto,
  UserDto,
} from '../dto/user.dto';
import { QuickCreateUserDto } from '../dto/quick-create.dto';
import { UserSettingsDto } from '../dto/user-settings.dto';
import { FirebaseAuthGuard, RolesGuard } from '../../auth/guards/auth.guards';
import { Roles } from '../../auth/decorators/auth.decorators';
import { Role } from '../../common/enum/role.enum';
import { ResetPasswordRequestDto } from '../dto/reset-password-request.dto';
import { ChangePasswordDto } from '../dto/change-password.dto';

@ApiTags('User Management')
@Controller('users')
@UsePipes(new ValidationPipe({ transform: true }))
@UseGuards(FirebaseAuthGuard)
@ApiBearerAuth('JWT-auth')
export class UserManagerController {
  constructor(
    private readonly userManagerService: UserManagerService,
    private readonly userDtoService: UserDtoService,
    private readonly mapperService: MapperService,
  ) {}

  @Post()
  @ApiOperation({ summary: 'Create a new user' })
  @ApiResponse({
    status: 201,
    description: 'User created successfully',
    type: UserDto,
  })
  @ApiResponse({ status: 409, description: 'User already exists' })
  @UseGuards(RolesGuard)
  @Roles(Role.ADMIN)
  async create(@Body() createUserDto: CreateUserDto): Promise<UserDto> {
    const result = await this.userManagerService.create(createUserDto);
    return this.userDtoService.getUserByIdAsDto(result.user.id);
  }

  @Post('with-firebase')
  @ApiOperation({
    summary: 'Create a new user with Firebase integration and default settings',
  })
  @ApiResponse({
    status: 201,
    description: 'User created successfully with Firebase and settings',
    type: UserDto,
  })
  @ApiResponse({ status: 409, description: 'User already exists' })
  @ApiResponse({ status: 400, description: 'Invalid Firebase ID or data' })
  @UseGuards(RolesGuard)
  @Roles(Role.ADMIN)
  async createWithFirebase(
    @Body() createUserWithSettingsDto: CreateUserWithSettingsDto,
  ): Promise<UserDto> {
    const result = await this.userManagerService.createUserWithFirebase(
      createUserWithSettingsDto,
    );
    return this.userDtoService.getUserByIdAsDto(result.user.id);
  }

  @Post('quick-create')
  @ApiOperation({
    summary:
      'Quick create user with email and full name, auto-generate password and Firebase account',
  })
  @ApiResponse({
    status: 201,
    description: 'User created successfully with auto-generated password',
  })
  @ApiResponse({ status: 409, description: 'User already exists' })
  @ApiResponse({ status: 400, description: 'Invalid data' })
  @UseGuards(RolesGuard)
  @Roles(Role.ADMIN)
  async quickCreateUser(@Body() quickCreateUserDto: QuickCreateUserDto) {
    return this.userManagerService.quickCreateUser(quickCreateUserDto);
  }

  @Post('bulk-create')
  @ApiOperation({ summary: 'Bulk create multiple users' })
  @ApiResponse({ status: 201, description: 'Users created successfully' })
  @ApiResponse({ status: 400, description: 'Invalid data' })
  @UseGuards(RolesGuard)
  @Roles(Role.ADMIN)
  async bulkCreateUsers(@Body() usersData: QuickCreateUserDto[]) {
    return this.userManagerService.bulkCreateUsers(usersData);
  }

  @Post('request-reset-password')
  @ApiOperation({
    summary:
      'Request password reset (generate new password and send to user email)',
  })
  @ApiResponse({ status: 201, description: 'Password reset request processed' })
  @ApiResponse({ status: 404, description: 'User not found' })
  async requestResetPassword(@Body() dto: ResetPasswordRequestDto) {
    return this.userManagerService.resetPasswordByEmail(dto.email);
  }

  @Post('change-password')
  @ApiOperation({ summary: 'Change password for logged-in user' })
  @ApiResponse({ status: 200, description: 'Password changed successfully' })
  @ApiResponse({ status: 400, description: 'Old password is incorrect' })
  async changePassword(@Body() dto: ChangePasswordDto, @Req() req) {
    const email = req.user?.email;
    return this.userManagerService.changePassword(
      email,
      dto.oldPassword,
      dto.newPassword,
      req.ip || req.headers['x-forwarded-for'],
      req.headers['user-agent'],
    );
  }

  @Get()
  @ApiOperation({ summary: 'Get all users as DTOs' })
  @ApiResponse({
    status: 200,
    description: 'List of all users as DTOs',
    type: [UserDto],
  })
  @UseGuards(RolesGuard)
  @Roles(Role.ADMIN)
  async findAll(): Promise<UserDto[]> {
    return this.userDtoService.getAllUsersAsDtos();
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get user by ID as DTO' })
  @ApiParam({ name: 'id', description: 'User ID (UUID)' })
  @ApiResponse({ status: 200, description: 'User found as DTO', type: UserDto })
  @ApiResponse({ status: 404, description: 'User not found' })
  async findOne(@Param('id') id: string): Promise<UserDto> {
    return this.userDtoService.getUserByIdAsDto(id);
  }

  @Get(':id/complete')
  @ApiOperation({
    summary: 'Get user with all related data as DTO (profile, settings)',
  })
  @ApiParam({ name: 'id', description: 'User ID (UUID)' })
  @ApiResponse({ status: 200, description: 'User with all data found as DTO' })
  @ApiResponse({ status: 404, description: 'User not found' })
  async getUserWithAllData(@Param('id') id: string) {
    return this.userDtoService.getUserWithProfileAndSettingsAsDto(id);
  }

  @Get('firebase/:firebaseId')
  @ApiOperation({ summary: 'Get user by Firebase ID as DTO' })
  @ApiParam({ name: 'firebaseId', description: 'Firebase User ID' })
  @ApiResponse({ status: 200, description: 'User found as DTO', type: UserDto })
  @ApiResponse({ status: 404, description: 'User not found' })
  async findByFirebaseId(
    @Param('firebaseId') firebaseId: string,
  ): Promise<UserDto> {
    const user = await this.userManagerService.findByFirebaseId(firebaseId);
    return this.userDtoService.getUserByIdAsDto(user.id);
  }

  @Get(':id/settings')
  @ApiOperation({ summary: 'Get user settings as DTOs' })
  @ApiParam({ name: 'id', description: 'User ID (UUID)' })
  @ApiResponse({
    status: 200,
    description: 'User settings retrieved as DTOs',
    type: [UserSettingsDto],
  })
  @ApiResponse({ status: 404, description: 'User not found' })
  async getUserSettings(@Param('id') id: string): Promise<UserSettingsDto[]> {
    return this.userDtoService.getUserSettingsAsDtos(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update user' })
  @ApiParam({ name: 'id', description: 'User ID (UUID)' })
  @ApiResponse({
    status: 200,
    description: 'User updated successfully',
    type: UserDto,
  })
  @ApiResponse({ status: 404, description: 'User not found' })
  @UseGuards(RolesGuard)
  @Roles(Role.ADMIN)
  async update(
    @Param('id') id: string,
    @Body() updateUserDto: UpdateUserDto,
  ): Promise<UserDto> {
    await this.userManagerService.update(id, updateUserDto);
    return this.userDtoService.getUserByIdAsDto(id);
  }

  @Patch(':id/settings')
  @ApiOperation({ summary: 'Update user settings' })
  @ApiParam({ name: 'id', description: 'User ID (UUID)' })
  @ApiResponse({
    status: 200,
    description: 'User settings updated successfully',
    type: [UserSettingsDto],
  })
  @ApiResponse({ status: 404, description: 'User not found' })
  async updateUserSettings(
    @Param('id') id: string,
    @Body() settings: Record<string, string>,
  ): Promise<UserSettingsDto[]> {
    await this.userManagerService.updateUserSettings(id, settings);
    return this.userDtoService.getUserSettingsAsDtos(id);
  }

  @Patch(':id/settings/:key')
  @ApiOperation({ summary: 'Update a specific user setting by key' })
  @ApiParam({ name: 'id', description: 'User ID (UUID)' })
  @ApiParam({ name: 'key', description: 'Setting key' })
  @ApiResponse({
    status: 200,
    description: 'User setting updated successfully',
    type: UserSettingsDto,
  })
  @ApiResponse({ status: 404, description: 'User or setting not found' })
  async updateUserSettingByKey(
    @Param('id') id: string,
    @Param('key') key: string,
    @Body('value') value: string,
  ): Promise<UserSettingsDto> {
    return this.userManagerService.updateUserSettingByKey(id, key, value);
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Delete user' })
  @ApiParam({ name: 'id', description: 'User ID (UUID)' })
  @ApiResponse({ status: 204, description: 'User deleted successfully' })
  @ApiResponse({ status: 404, description: 'User not found' })
  @UseGuards(RolesGuard)
  @Roles(Role.ADMIN)
  async remove(@Param('id') id: string) {
    return this.userManagerService.remove(id);
  }
}
