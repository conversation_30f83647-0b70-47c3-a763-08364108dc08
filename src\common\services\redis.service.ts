import { Injectable, Inject, Logger } from '@nestjs/common';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';

export interface CacheOptions {
  ttl?: number;
  prefix?: string;
}

@Injectable()
export class RedisService {
  private readonly logger = new Logger(RedisService.name);
  private readonly defaultTTL = 3600; // 1 hour

  constructor(@Inject(CACHE_MANAGER) private cacheManager: Cache) {}

  /**
   * Set cache value
   */
  async set(key: string, value: any, options?: CacheOptions): Promise<void> {
    try {
      const cacheKey = this.buildKey(key, options?.prefix);
      const ttl = options?.ttl || this.defaultTTL;

      this.logger.log(`Attempting to set cache: ${cacheKey} with TTL: ${ttl}s`);
      await this.cacheManager.set(cacheKey, value, ttl);
      this.logger.log(`Cache set successfully: ${cacheKey}`);
    } catch (error) {
      this.logger.error(`Failed to set cache for key ${key}:`, error);
      throw error;
    }
  }

  /**
   * Get cache value
   */
  async get<T>(key: string, options?: CacheOptions): Promise<T | null> {
    try {
      const cacheKey = this.buildKey(key, options?.prefix);
      this.logger.log(`Attempting to get cache: ${cacheKey}`);

      const value = await this.cacheManager.get<T>(cacheKey);

      if (value) {
        this.logger.log(`Cache hit: ${cacheKey}`);
      } else {
        this.logger.log(`Cache miss: ${cacheKey}`);
      }

      return value || null;
    } catch (error) {
      this.logger.error(`Failed to get cache for key ${key}:`, error);
      return null;
    }
  }

  /**
   * Delete cache key
   */
  async del(key: string, options?: CacheOptions): Promise<void> {
    try {
      const cacheKey = this.buildKey(key, options?.prefix);
      await this.cacheManager.del(cacheKey);
      this.logger.debug(`Cache deleted: ${cacheKey}`);
    } catch (error) {
      this.logger.error(`Failed to delete cache for key ${key}:`, error);
      throw error;
    }
  }

  /**
   * Delete multiple cache keys by pattern
   */
  async delPattern(pattern: string): Promise<void> {
    try {
      // Note: This is a simplified implementation
      this.logger.debug(`Cache pattern deletion requested: ${pattern}`);
    } catch (error) {
      this.logger.error(`Failed to delete cache pattern ${pattern}:`, error);
      throw error;
    }
  }

  /**
   * Check if key exists
   */
  async exists(key: string, options?: CacheOptions): Promise<boolean> {
    try {
      const cacheKey = this.buildKey(key, options?.prefix);
      const value = await this.cacheManager.get(cacheKey);
      return value !== null && value !== undefined;
    } catch (error) {
      this.logger.error(
        `Failed to check cache existence for key ${key}:`,
        error,
      );
      return false;
    }
  }

  /**
   * Get cache statistics
   */
  async getStats(): Promise<any> {
    try {
      // This is a basic implementation
      // In production, you might want to use Redis INFO command
      return {
        status: 'connected',
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error('Failed to get cache stats:', error);
    }
  }

  /**
   * Clear all cache
   */
  async reset(): Promise<void> {
    try {
      // Note: reset() method is not available in newer versions
      // In production, you might want to use Redis FLUSHDB command
      await this.cacheManager.clear();
      this.logger.debug('Cache reset requested');
    } catch (error) {
      this.logger.error('Failed to reset cache:', error);
      throw error;
    }
  }

  /**
   * Build cache key with prefix
   */
  private buildKey(key: string, prefix?: string): string {
    if (prefix) {
      return `${prefix}:${key}`;
    }
    return key;
  }

  /**
   * Generate user cache key
   */
  generateUserKey(userId: string): string {
    return `user:${userId}`;
  }

  /**
   * Generate user profile cache key
   */
  generateUserProfileKey(userId: string): string {
    return `user_profile:${userId}`;
  }

  /**
   * Generate user settings cache key
   */
  generateUserSettingsKey(userId: string): string {
    return `user_settings:${userId}`;
  }

  /**
   * Generate user by email cache key
   */
  generateUserByEmailKey(email: string): string {
    return `user_email:${email}`;
  }

  /**
   * Generate user by Firebase ID cache key
   */
  generateUserByFirebaseIdKey(firebaseId: string): string {
    return `user_firebase:${firebaseId}`;
  }

  /**
   * Invalidate all user-related cache
   */
  async invalidateUserCache(userId: string): Promise<void> {
    try {
      const keys = [
        this.generateUserKey(userId),
        this.generateUserProfileKey(userId),
        this.generateUserSettingsKey(userId),
        `user_dto:${userId}`,
        `${this.generateUserSettingsKey(userId)}:object`,
        `${this.generateUserSettingsKey(userId)}:theme`,
        `${this.generateUserSettingsKey(userId)}:language`,
        `${this.generateUserSettingsKey(userId)}:notifications_enabled`,
        `${this.generateUserSettingsKey(userId)}:email_notifications`,
        `${this.generateUserSettingsKey(userId)}:push_notifications`,
        `${this.generateUserSettingsKey(userId)}:auto_save`,
        `${this.generateUserSettingsKey(userId)}:sound_enabled`,
        `${this.generateUserSettingsKey(userId)}:timezone`,
        `${this.generateUserSettingsKey(userId)}:date_format`,
        `${this.generateUserSettingsKey(userId)}:time_format`,
        `${this.generateUserSettingsKey(userId)}:privacy_level`,
        `${this.generateUserSettingsKey(userId)}:email_frequency`,
        `${this.generateUserSettingsKey(userId)}:dashboard_layout`,
        `${this.generateUserSettingsKey(userId)}:sidebar_collapsed`,
        `${this.generateUserSettingsKey(userId)}:compact_mode`,
      ];

      await Promise.all(keys.map((key) => this.del(key)));
      this.logger.debug(`Invalidated cache for user ${userId}`);
    } catch (error) {
      this.logger.error(
        `Failed to invalidate cache for user ${userId}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Invalidate all users cache (for bulk operations)
   */
  async invalidateAllUsersCache(): Promise<void> {
    try {
      const keys = [
        'all_users_dtos',
        'all_users',
        'all_users_profiles',
        'all_users_settings',
      ];

      await Promise.all(keys.map((key) => this.del(key)));
      this.logger.debug('Invalidated all users cache');
    } catch (error) {
      this.logger.error('Failed to invalidate all users cache:', error);
      throw error;
    }
  }

  /**
   * Invalidate user by email cache
   */
  async invalidateUserByEmailCache(email: string): Promise<void> {
    try {
      await this.del(this.generateUserByEmailKey(email));
      this.logger.debug(`Invalidated cache for email ${email}`);
    } catch (error) {
      this.logger.error(
        `Failed to invalidate cache for email ${email}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Invalidate user by Firebase ID cache
   */
  async invalidateUserByFirebaseIdCache(firebaseId: string): Promise<void> {
    try {
      await this.del(this.generateUserByFirebaseIdKey(firebaseId));
      this.logger.debug(`Invalidated cache for Firebase ID ${firebaseId}`);
    } catch (error) {
      this.logger.error(
        `Failed to invalidate cache for Firebase ID ${firebaseId}:`,
        error,
      );
      throw error;
    }
  }
}
