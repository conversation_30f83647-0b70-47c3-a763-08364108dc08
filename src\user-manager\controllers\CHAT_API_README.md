# Chat Service API Documentation

This document describes the public APIs created for Chat Service to access user information.

## Authentication

The Chat Service APIs use service-to-service authentication with API keys. You need to include the API key in your requests using one of these methods:

### Method 1: X-API-Key Header (Recommended)
```http
X-API-Key: your-chat-service-api-key
```

### Method 2: Authorization Header with Service scheme
```http
Authorization: Service your-chat-service-api-key
```

### Method 3: Query Parameter (Development only)
```http
GET /api/users/123?apiKey=your-chat-service-api-key
```

## Environment Configuration

Add these environment variables to your `.env` file:

```env
# Chat Service API Key
CHAT_SERVICE_API_KEY=your-secure-api-key-here

# Or multiple service API keys (comma-separated)
SERVICE_API_KEYS=chat-service-key,other-service-key
```

## API Endpoints

### 1. Get User by ID
```http
GET /api/users/{userId}
X-API-Key: your-api-key
```

**Response:**
```json
{
  "userId": "123e4567-e89b-12d3-a456-426614174000",
  "email": "<EMAIL>",
  "displayName": "John Doe",
  "role": "USER",
  "emailVerified": true,
  "enabled": true,
  "avatarUrl": "https://example.com/avatar.jpg",
  "phoneNumber": "+1234567890",
  "company": "Bright Company",
  "occupation": "Software Engineer"
}
```

### 2. Get Multiple Users by IDs
```http
POST /api/users/batch
X-API-Key: your-api-key
Content-Type: application/json

{
  "userIds": [
    "123e4567-e89b-12d3-a456-426614174000",
    "456e7890-e89b-12d3-a456-426614174001"
  ]
}
```

**Response:**
```json
[
  {
    "userId": "123e4567-e89b-12d3-a456-426614174000",
    "email": "<EMAIL>",
    "displayName": "John Doe",
    "role": "USER",
    "emailVerified": true,
    "enabled": true
  },
  {
    "userId": "456e7890-e89b-12d3-a456-426614174001",
    "email": "<EMAIL>",
    "displayName": "Jane Smith",
    "role": "ADMIN",
    "emailVerified": true,
    "enabled": true
  }
]
```

### 3. Search Users
```http
GET /api/users?q=john&limit=10
X-API-Key: your-api-key
```

**Query Parameters:**
- `q` (required): Search query
- `limit` (optional): Maximum number of results (default: 10, max: 100)

### 4. Check if User Exists
```http
GET /api/users/{userId}/exists
X-API-Key: your-api-key
```

**Response:**
```json
true
```

### 5. Validate User IDs
```http
POST /api/users/validate
X-API-Key: your-api-key
Content-Type: application/json

{
  "userIds": [
    "123e4567-e89b-12d3-a456-426614174000",
    "invalid-user-id",
    "456e7890-e89b-12d3-a456-426614174001"
  ]
}
```

**Response:**
```json
[
  "123e4567-e89b-12d3-a456-426614174000",
  "456e7890-e89b-12d3-a456-426614174001"
]
```

### 6. Get User Role
```http
GET /api/users/{userId}/role
X-API-Key: your-api-key
```

**Response:**
```json
"USER"
```

### 7. Get User Profile
```http
GET /api/users/{userId}/profile
X-API-Key: your-api-key
```

**Response:**
```json
{
  "userId": "123e4567-e89b-12d3-a456-426614174000",
  "email": "<EMAIL>",
  "displayName": "John Doe",
  "role": "USER",
  "emailVerified": true,
  "enabled": true
}
```

## Error Responses

### 401 Unauthorized
```json
{
  "statusCode": 401,
  "message": "Service API key required",
  "error": "Unauthorized"
}
```

### 404 Not Found
```json
{
  "statusCode": 404,
  "message": "User with ID 123 not found",
  "error": "Not Found"
}
```

### 400 Bad Request
```json
{
  "statusCode": 400,
  "message": "User IDs array cannot be empty",
  "error": "Bad Request"
}
```

## Caching

All endpoints implement Redis caching with a 5-minute TTL to improve performance. Cache keys are automatically invalidated when user data is updated.

## Rate Limiting

Consider implementing rate limiting for production use to prevent abuse.

## Java Client Integration

The APIs are designed to be compatible with your existing Java UserServiceClient. Update your service configuration:

```java
@Value("${user-service.base-url:http://localhost:3000}")
private String userServiceBaseUrl;

@Value("${user-service.api-key}")
private String apiKey;
```

Add the API key to your HTTP headers:
```java
HttpHeaders headers = new HttpHeaders();
headers.set("X-API-Key", apiKey);
HttpEntity<?> entity = new HttpEntity<>(headers);
```

## Security Notes

1. Keep your API keys secure and rotate them regularly
2. Use HTTPS in production
3. Consider implementing IP whitelisting for additional security
4. Monitor API usage and implement alerting for unusual patterns
