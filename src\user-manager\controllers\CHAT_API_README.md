# Chat Service API Documentation

This document describes the public APIs created for Chat Service to access user information.

## Authentication

The Chat Service APIs use service-to-service authentication with API keys. You need to include the API key in your requests using one of these methods:

### Method 1: X-API-Key Header (Recommended)
```http
X-API-Key: your-chat-service-api-key
X-Service-Name: chat-service
X-Service-Version: 1.0.0
```

### Method 2: Authorization Header with Service scheme
```http
Authorization: Service your-chat-service-api-key
X-Service-Name: chat-service
X-Service-Version: 1.0.0
```

### Method 3: Query Parameter (Development only)
```http
GET /api/users/123?apiKey=your-chat-service-api-key
```

**Note:** The `X-Service-Name` and `X-Service-Version` headers are optional but recommended for better logging and monitoring.

## Environment Configuration

Add these environment variables to your `.env` file:

```env
# Chat Service API Key
CHAT_SERVICE_API_KEY=your-secure-api-key-here

# Or multiple service API keys (comma-separated)
SERVICE_API_KEYS=chat-service-key,other-service-key
```

## API Endpoints

### 1. Get User by ID
```http
GET /api/users/{userId}
X-API-Key: your-api-key
```

**Response:**
```json
{
  "userId": "123e4567-e89b-12d3-a456-426614174000",
  "email": "<EMAIL>",
  "displayName": "John <PERSON>e",
  "role": "USER",
  "emailVerified": true,
  "enabled": true,
  "avatarUrl": "https://example.com/avatar.jpg",
  "phoneNumber": "+1234567890",
  "company": "Bright Company",
  "occupation": "Software Engineer"
}
```

### 2. Get Multiple Users by IDs
```http
POST /api/users/batch
X-API-Key: your-api-key
Content-Type: application/json

{
  "userIds": [
    "123e4567-e89b-12d3-a456-426614174000",
    "456e7890-e89b-12d3-a456-426614174001"
  ]
}
```

**Response:**
```json
[
  {
    "userId": "123e4567-e89b-12d3-a456-426614174000",
    "email": "<EMAIL>",
    "displayName": "John Doe",
    "role": "USER",
    "emailVerified": true,
    "enabled": true
  },
  {
    "userId": "456e7890-e89b-12d3-a456-426614174001",
    "email": "<EMAIL>",
    "displayName": "Jane Smith",
    "role": "ADMIN",
    "emailVerified": true,
    "enabled": true
  }
]
```

### 3. Search Users
```http
GET /api/users?q=john&limit=10
X-API-Key: your-api-key
```

**Query Parameters:**
- `q` (required): Search query
- `limit` (optional): Maximum number of results (default: 10, max: 100)

### 4. Check if User Exists
```http
GET /api/users/{userId}/exists
X-API-Key: your-api-key
```

**Response:**
```json
true
```

### 5. Validate User IDs
```http
POST /api/users/validate
X-API-Key: your-api-key
Content-Type: application/json

{
  "userIds": [
    "123e4567-e89b-12d3-a456-426614174000",
    "invalid-user-id",
    "456e7890-e89b-12d3-a456-426614174001"
  ]
}
```

**Response:**
```json
[
  "123e4567-e89b-12d3-a456-426614174000",
  "456e7890-e89b-12d3-a456-426614174001"
]
```

### 6. Get User Role
```http
GET /api/users/{userId}/role
X-API-Key: your-api-key
```

**Response:**
```json
"USER"
```

### 7. Get User Profile
```http
GET /api/users/{userId}/profile
X-API-Key: your-api-key
```

**Response:**
```json
{
  "userId": "123e4567-e89b-12d3-a456-426614174000",
  "email": "<EMAIL>",
  "displayName": "John Doe",
  "role": "USER",
  "emailVerified": true,
  "enabled": true
}
```

## Error Responses

### 401 Unauthorized
```json
{
  "statusCode": 401,
  "message": "Service API key required",
  "error": "Unauthorized"
}
```

### 404 Not Found
```json
{
  "statusCode": 404,
  "message": "User with ID 123 not found",
  "error": "Not Found"
}
```

### 400 Bad Request
```json
{
  "statusCode": 400,
  "message": "User IDs array cannot be empty",
  "error": "Bad Request"
}
```

## Caching

All endpoints implement Redis caching with a 5-minute TTL to improve performance. Cache keys are automatically invalidated when user data is updated.

## Rate Limiting

Consider implementing rate limiting for production use to prevent abuse.

## Java Client Integration

### Option 1: Manual Header Addition
Update your service configuration:

```java
@Value("${user-service.base-url:http://localhost:3000}")
private String userServiceBaseUrl;

@Value("${user-service.api-key}")
private String apiKey;
```

Add the API key to your HTTP headers:
```java
HttpHeaders headers = new HttpHeaders();
headers.set("X-API-Key", apiKey);
headers.set("X-Service-Name", "chat-service");
headers.set("X-Service-Version", "1.0.0");
HttpEntity<?> entity = new HttpEntity<>(headers);
```

### Option 2: HTTP Request Interceptor (Recommended)
Use the provided `ChatServiceInterceptor.java` to automatically add headers to all requests:

```java
@Autowired
private RestTemplate restTemplate; // Will automatically include auth headers

// Your existing service methods work without modification
ResponseEntity<UserInfo> response = restTemplate.exchange(
    url, HttpMethod.GET, null, UserInfo.class
);
```

The interceptor automatically adds:
- `X-API-Key`: Your service API key
- `X-Service-Name`: Your service name
- `X-Service-Version`: Your service version
- `Content-Type`: application/json (if not set)

## Security Notes

1. Keep your API keys secure and rotate them regularly
2. Use HTTPS in production
3. Consider implementing IP whitelisting for additional security
4. Monitor API usage and implement alerting for unusual patterns
